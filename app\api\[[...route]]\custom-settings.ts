import { Hono } from "hono";
import prisma from "@/lib/prisma";

const app = new Hono();

app.post("/", async (c) => {
  try {
    const { type, value, weight, tagIds, tagNames, tags } = await c.req.json<{
      type: string;
      value: string;
      weight?: number;
      tagIds?: string[];
      tagNames?: string[];
      tags?: { name: string; description?: string }[];
    }>();

    let finalTagIds: string[] = tagIds || [];

    // Handle new tags array format (with descriptions)
    if (tags && tags.length > 0) {
      for (const tagData of tags) {
        let tag = await prisma.tag.findUnique({
          where: { name: tagData.name },
        });

        if (!tag) {
          tag = await prisma.tag.create({
            data: {
              name: tagData.name,
              description: tagData.description
            },
          });
        } else if (tagData.description && !tag.description) {
          // Update existing tag with description if it doesn't have one
          tag = await prisma.tag.update({
            where: { id: tag.id },
            data: { description: tagData.description },
          });
        }
        finalTagIds.push(tag.id);
      }
    }
    // Fallback to old tagNames format for backward compatibility
    else if (tagNames && tagNames.length > 0) {
      for (const tagName of tagNames) {
        let tag = await prisma.tag.findUnique({
          where: { name: tagName },
        });

        if (!tag) {
          tag = await prisma.tag.create({
            data: { name: tagName },
          });
        }
        finalTagIds.push(tag.id);
      }
    }

    if (!type || !value) {
      return c.json({ error: "Type and value are required." }, 400);
    }

    let setting;
    const existingSetting = await prisma.customChatSetting.findUnique({
      where: { value: value },
    });

    if (existingSetting) {
      // Update existing setting, including weight if provided
      setting = await prisma.customChatSetting.update({
        where: { id: existingSetting.id },
        data: {
          type, // Ensure type is also updated if needed, though value is unique
          value,
          weight: weight !== undefined ? weight : existingSetting.weight, // Update weight or keep existing
          tags: finalTagIds.length > 0 ? {
            set: finalTagIds.map(id => ({ id })) // Connect existing tags by ID
          } : undefined, // If tagIds is not provided, don't modify tags
        },
      });
      return c.json(setting, 200); // Return 200 for update
    } else {
      // Create new setting
      setting = await prisma.customChatSetting.create({
        data: {
          type,
          value,
          weight: weight !== undefined ? weight : 10, // Use provided weight or default
          tags: finalTagIds.length > 0 ? {
            connect: finalTagIds.map(id => ({ id })) // Connect existing tags by ID
          } : undefined,
        },
      });
      return c.json(setting, 201); // Return 201 for creation
    }
  } catch (error) {
    console.error("Error adding/updating custom setting:", error);
    return c.json({ error: "Failed to add/update custom setting." }, 500);
  }
});

app.get("/", async (c) => {
  try {
    const { type } = c.req.query();
    let settings;
    if (type) {
      settings = await prisma.customChatSetting.findMany({
        where: { type: type },
        include: { tags: true }, // Include tags
      });
    } else {
      settings = await prisma.customChatSetting.findMany({
        include: { tags: true }, // Include tags
      });
    }
    return c.json(settings);
  } catch (error) {
    console.error("Error fetching custom settings:", error);
    return c.json({ error: "Failed to fetch custom settings." }, 500);
  }
});

app.delete("/:id", async (c) => {
  try {
    const { id } = c.req.param();

    if (!id) {
      return c.json({ error: "ID is required." }, 400);
    }

    const deletedSetting = await prisma.customChatSetting.delete({
      where: { id },
    });

    return c.json(deletedSetting, 200);
  } catch (error) {
    console.error("Error deleting custom setting:", error);
    return c.json({ error: "Failed to delete custom setting." }, 500);
  }
});

app.post("/tags", async (c) => {
  try {
    const { name, description } = await c.req.json<{ name: string; description?: string }>();
    if (!name) {
      return c.json({ error: "Tag name is required." }, 400);
    }
    const newTag = await prisma.tag.create({
      data: { name, description },
    });
    return c.json(newTag, 201);
  } catch (error) {
    console.error("Error creating tag:", error);
    return c.json({ error: "Failed to create tag." }, 500);
  }
});

app.get("/tags", async (c) => {
  try {
    const tags = await prisma.tag.findMany();
    return c.json(tags);
  } catch (error) {
    console.error("Error fetching tags:", error);
    return c.json({ error: "Failed to fetch tags." }, 500);
  }
});

app.put("/tags/:id", async (c) => {
  try {
    const { id } = c.req.param();
    const { name, description } = await c.req.json<{ name?: string; description?: string }>();
    if (!id) {
      return c.json({ error: "Tag ID is required." }, 400);
    }
    const updatedTag = await prisma.tag.update({
      where: { id },
      data: { ...(name && { name }), ...(description !== undefined && { description }) },
    });
    return c.json(updatedTag, 200);
  } catch (error) {
    console.error("Error updating tag:", error);
    return c.json({ error: "Failed to update tag." }, 500);
  }
});

app.delete("/tags/:id", async (c) => {
  try {
    const { id } = c.req.param();
    if (!id) {
      return c.json({ error: "Tag ID is required." }, 400);
    }
    const deletedTag = await prisma.tag.delete({
      where: { id },
    });
    return c.json(deletedTag, 200);
  } catch (error) {
    console.error("Error deleting tag:", error);
    return c.json({ error: "Failed to delete tag." }, 500);
  }
});

export default app;