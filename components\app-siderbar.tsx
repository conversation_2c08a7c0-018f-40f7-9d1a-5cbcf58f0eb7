'use client';

import React, { useEffect, useState } from 'react';
import { Calendar, Home, Inbox, Search, Settings, Trash2, FolderTree, Book, Bookmark } from "lucide-react"
import { Button } from './ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { useManageDisplayTheme } from '@/hooks/use-manage-theme-display';
import { ListPlus } from 'lucide-react';
import { ScrollArea } from './ui/scroll-area';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from './ui/accordion';

interface Theme {
  id: string;
  theme: string;
  subCategory: SubCategory[];
  nouns: any; // Replace with proper type from your schema
  verbs: any; // Replace with proper type from your schema
  adjectives: any; // Replace with proper type from your schema
  adverbs: any; // Replace with proper type from your schema
}

interface SubCategory {
  id?: string;
  theme: string;
}

interface ThemeGroup {
  name: string;
  themes: Theme[];
}

export function AppSidebar() {
  const [themeGroups, setThemeGroups] = useState<ThemeGroup[]>([]);
  const [error, setError] = useState("");
  const [expandedGroups, setExpandedGroups] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState(""); // Add this state
  const { setTheme, deleteTheme } = useManageDisplayTheme();

  const organizeThemes = (themes: Theme[]) => {
    // Filter themes based on search query
    const filteredThemes = themes.filter(theme => 
      theme.theme.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const groups = filteredThemes.reduce((acc, theme) => {
      const firstLetter = theme.theme[0].toUpperCase();
      if (!acc[firstLetter]) {
        acc[firstLetter] = [];
      }
      acc[firstLetter].push(theme);
      return acc;
    }, {} as { [key: string]: Theme[] });

    return Object.entries(groups)
      .map(([name, themes]) => ({
        name,
        themes: themes.sort((a, b) => a.theme.localeCompare(b.theme))
      }))
      .sort((a, b) => a.name.localeCompare(b.name));
  };

  const fetchThemes = async () => {
    try {
      const response = await fetch(`/api/vocabulary/all-themes`);
      const { data } = await response.json();
      const organized = organizeThemes(data);
      setThemeGroups(organized);
      setExpandedGroups(organized.map(group => group.name));
    } catch (err) {
      setError('Failed to fetch themes');
    }
  };

  useEffect(() => {
    fetchThemes();
  }, []); // Initial fetch

  // Add this effect to update groups when search changes
  useEffect(() => {
    const response = fetch(`/api/vocabulary/all-themes`)
      .then(res => res.json())
      .then(({ data }) => {
        const organized = organizeThemes(data);
        setThemeGroups(organized);
      })
      .catch(() => setError('Failed to update themes'));
  }, [searchQuery]); // Re-organize when search changes

  const handleDeleteTheme = async (id: string, themeName: string) => {
    if (window.confirm(`Are you sure you want to delete "${themeName}"?`)) {
      const success = await deleteTheme(id);
      if (!success) {
        setError("Failed to delete theme");
      }
    }
  };

  return (
    <Sidebar variant="inset" className="bg-sidebar ">
      <SidebarContent>
        <SidebarGroup>
          <div className="flex flex-col gap-2 p-2 bg-sidebar-accent rounded-md">
            <div className="flex items-center justify-between">
              <SidebarGroupLabel className="text-md flex items-center gap-2 text-sidebar-foreground">
                <FolderTree size={18} />
                Vocabulary Themes
              </SidebarGroupLabel>
            
            </div>
            {/* Add search input */}
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-sidebar-foreground/50" />
              <input
                type="text"
                placeholder="Search themes..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-8 pr-2 py-1 text-sm bg-sidebar border border-sidebar-border rounded-md 
                          focus:outline-none focus:ring-1 focus:ring-sidebar-primary
                          placeholder:text-sidebar-foreground/50"
              />
            </div>
          </div>
          <SidebarGroupContent>
            <ScrollArea className="h-[calc(100vh-200px)]">
              {error ? (
                <p className="text-destructive p-2">{error}</p>
              ) : (
                <Accordion 
                  type="multiple" 
                  value={expandedGroups}
                  onValueChange={setExpandedGroups}
                  className="w-full space-y-2"
                >
                  {themeGroups.map((group) => (
                    <AccordionItem 
                      key={group.name} 
                      value={group.name}
                      className="border-none bg-sidebar-accent rounded-md overflow-hidden"
                    >
                      <AccordionTrigger className="text-sm py-2 px-4 hover:bg-sidebar-accent/80 hover:no-underline">
                        <div className="flex items-center gap-2 text-sidebar-foreground">
                          <Book size={16} className="text-sidebar-primary" />
                          {group.name} ({group.themes.length})
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="px-2 pb-2">
                        <div className="flex flex-col gap-2">
                          {group.themes.map((theme) => (
                            <div key={theme.id} className="rounded-md border border-sidebar-border bg-sidebar p-2">
                              <div className="flex items-center justify-between gap-1">
                                <Button 
                                  onClick={() => {
                                    setTheme({
                                      theme: theme.theme,
                                      id: theme.id
                                    });
                                  }}
                                  variant="ghost"
                                  className="text-sm w-full justify-start text-sidebar-foreground hover:bg-sidebar-accent"
                                >
                                  <Bookmark size={14} className="mr-2 text-sidebar-primary" />
                                  {theme.theme}
                                </Button>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="p-1 hover:bg-sidebar-accent"
                                    >
                                      <ListPlus size={16} className="text-sidebar-primary" />
                                    </Button>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-48 p-2 bg-sidebar border-sidebar-border">
                                    <div className="flex flex-col gap-2">
                                      {theme.subCategory.map((subCategory) => (
                                        <div key={subCategory.theme} className="flex items-center justify-between">
                                          <Button 
                                            onClick={() => setTheme({ theme: subCategory.theme, id: subCategory.id || '' })}
                                            variant="outline" 
                                            size="sm"
                                            className="text-xs text-sidebar-foreground border-sidebar-border hover:bg-sidebar-accent"
                                          >
                                            {subCategory.theme}
                                            <div className={`ml-2 w-2 h-2 rounded-full ${
                                              subCategory.id ? "bg-sidebar-primary" : "bg-muted"
                                            }`} />
                                          </Button>
                                          {subCategory.id && (
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              className="p-1 hover:bg-destructive/10"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                handleDeleteTheme(subCategory.id!, subCategory.theme);
                                              }}
                                            >
                                              <Trash2 size={14} className="text-destructive" />
                                            </Button>
                                          )}
                                        </div>
                                      ))}
                                    </div>
                                  </PopoverContent>
                                </Popover>
                              </div>
                              <div className="flex items-center justify-between mt-2 pl-6">
                                {theme.subCategory.length > 0 && (
                                  <div className="text-xs text-sidebar-foreground/60">
                                    {theme.subCategory.length} subcategories
                                  </div>
                                )}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="p-1 hover:bg-destructive/10"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteTheme(theme.id, theme.theme);
                                  }}
                                >
                                  <Trash2 size={14} className="text-destructive" />
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              )}
            </ScrollArea>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
