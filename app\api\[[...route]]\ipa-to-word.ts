import { Hono } from "hono";
import {
  HumanMessage,
  SystemMessage,
  BaseMessage,
} from "@langchain/core/messages";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { Annotation } from "@langchain/langgraph";
import { tool } from "@langchain/core/tools";
import { ChatOpenAI } from "@langchain/openai";
import { Agent } from "@/lib/ai-functions";
import { z } from "zod";
import { XMLParser } from "fast-xml-parser";
import pLimit from "p-limit";

const searchtool = tool(
  (input) => {
    return "search result";
  },
  {
    name: "searchtool",
    description: "search tool",
    schema: z.string().describe("search to be made"),
  }
);

const app = new Hono().post("/", async (c) => {
  const { ipaInput, languages, partOfSpeech, customQuery } = await c.req.json<{
    ipaInput: string;
    languages: string[];
    partOfSpeech?: string;
    customQuery?: string;
  }>();

  let partOfSpeechInstruction = "";
  if (partOfSpeech && partOfSpeech !== "any") {
    partOfSpeechInstruction = `Focus specifically on ${partOfSpeech}.`;
  }

  let customQueryInstruction = "";
  if (customQuery && customQuery.trim()) {
    customQueryInstruction = `Additionally, consider this specific request: ${customQuery}`;
  }

  // Create a function to process a single language
  const processLanguage = async (language: string) => {
    const systemPrompt = `You are an IPA-to-word matching assistant. Given an IPA phonetic spelling, find words or phrases in ${language} that:
    1. Contain that exact IPA sound pattern, or
    2. Sound very similar to the provided IPA pattern or
    3. A word which sound similar or close (sounds similar with a tweak on one or two letters, changing a syllable,...)
    4: Adding one sylable in every position of the word try to find a matching or similar word ( you can also twik one or more letter or change entire sylables)

    ${partOfSpeechInstruction}
    ${customQueryInstruction}

    If the IPA input contains multiple syllables, you may provide:
    - Single words that match the entire pattern 
    - Single words that which sounds are close to the part of the pattern (sounds similar with a tweak on one or two letters, changing a syllable,...) or 
    - A sequence of words that together sound like the pattern
    - Short phrases or sentences that incorporate the sound
    
    For each suggestion, provide:
    1. The word/phrase in ${language}
    2. The exact IPA pronunciation
    3. A brief visual description to help memorize the word (1-2 sentences)
    
    Format your response as XML according to this structure:
    <matches>
      <match>
        <word>word or phrase</word>
        <ipa>IPA pronunciation</ipa>
        <visualDescription>brief description to help memorize</visualDescription>
      </match>
      <!-- Additional matches -->
    </matches>
    
    some exemples only for the cases Words starting with the /pi/ sound and  Words containing the /pi/ sound (medially or finally):"

    Words starting with the /pi/ sound:

pea /piː/
pee /piː/
peak /piːk/
peel /piːl/
peep /piːp/
peace /piːs/
piece /piːs/
people /piːpl/
pizza /piːtsə/
piano /piˈænəʊ/
pique /piːk/
.
.

Words containing the /pi/ sound (medially or finally):

happy /ˈhæpi/ (final /pi/)
copy /ˈkɒpi/ (final /pi/)
puppy /ˈpʌpi/ (final /pi/)
sleepy /ˈsliːpi/ (final /pi/)
crispy /ˈkrɪspi/ (final /pi/)
floppy /ˈflɒpi/ (final /pi/)
droopy /ˈdruːpi/ (final /pi/)
creepy /ˈkriːpi/ (final /pi/)
spooky /ˈspuːki/ (final /pi/)
grumpy /ˈɡrʌmpi/ (final /pi/)
dumpy /ˈdʌmpi/ (final /pi/)
bumpy /ˈbʌmpi/ (final /pi/)
jumpy /ˈdʒʌmpi/ (final /pi/)
therapy /ˈθɛrəpi/ (final /pi/)
recipe /ˈrɛsəpi/ (final /pi/)
Mississippi /mɪsəˈsɪpi/ (final /pi/)
Penelope /pəˈnɛləpi/ (final /pi/)
apiary /ˈeɪpiəri/ (contains /pi/)
utopia /juːˈtəʊpiə/ (contains /pi/)
Ethiopia /ˌiːθiˈəʊpiə/ (contains /pi/)
copies /ˈkɒpiz/ (contains /pi/)
happier /ˈhæpiər/ (contains /pi/)
.
.
.

`;

    const AgentState = Annotation.Root({
      messages: Annotation<BaseMessage[]>({
        reducer: (x, y) => x.concat(y),
        default: () => [new SystemMessage(systemPrompt)],
      }),
    });

    const litellmDeepSeek = new ChatOpenAI({
      model: "gemini/gemini-2.5-flash-preview-04-17",
      configuration: {
        basePath: "https://litellm.tadzlab.xyz",
      },
      apiKey: process.env.LITELLM_API_KEY,
    });

    const preAgent = new Agent({
      model: litellmDeepSeek,
      State: AgentState,
      tools: [searchtool],
    });
    const agent = preAgent.create();

    const humanMessage = new HumanMessage(ipaInput);

    let attempts = 0;
    while (attempts < 3) {
      try {
        attempts += 1;
        const response = await agent.invoke(
          { messages: [humanMessage] },
          { configurable: { thread_id: `ipa-to-word-${language}` } }
        );

        const xmlResult = await new StringOutputParser().invoke(
          response.messages[response.messages.length - 1]
        );

        // Parse XML to JSON
        const parser = new XMLParser({
          ignoreAttributes: false,
          isArray: (name) => name === "match",
        });

        // Wrap the XML in a root element if it doesn't have one
        const xmlWithRoot = xmlResult.includes("<matches>")
          ? xmlResult
          : `<matches>${xmlResult}</matches>`;

        const jsonObj = parser.parse(xmlWithRoot);

        // Extract matches array or provide empty array if no matches
        const matches =
          jsonObj.matches && jsonObj.matches.match ? jsonObj.matches.match : [];

        // Add language to each match
        return matches.map((match: any) => ({
          ...match,
          language,
        }));
      } catch (error) {
        console.error(`Error processing language ${language}:`, error);
        if (attempts >= 3) {
          return [];
        }
      }
    }
    return [];
  };

  try {
    // Limit concurrent requests to 3 at a time
    const limit = pLimit(3);

    // Process all languages concurrently with the limit
    const allResults = await Promise.all(
      languages.map((language) => limit(() => processLanguage(language)))
    );

    // Flatten the results
    const matches = allResults.flat();

    return c.json({ matches });
  } catch (error) {
    console.error("Error in IPA-to-word route:", error);
    return c.json({ error: "Failed to process IPA input" }, 500);
  }
});

export default app;
