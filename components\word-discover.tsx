"use client";

import React, { useState } from "react";
import { schemaDiscovery } from "@/lib/schemas";
import { z } from "zod";

// Import popover components from shadcn-ui
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import WordCard from "./word-card";

// Adjust these types to match your actual usage or Zod schema
/* type TUsageFrequency = {
  speaking: number;
  writing: number;
};
 */
type DiscoverySchema = z.infer<typeof schemaDiscovery>
type IVocabData = Pick<DiscoverySchema,"primaryMeaning" | "exampleSentences" | "otherMeanings" | "synonyms" | "usageFrequency">

/* type IVocabData {
  primaryMeaning: [string, string];
  exampleSentences: [string, string][];     
  otherMeanings: [string, string][];
  synonyms: string[];
  usageFrequency: TUsageFrequency;
} */

  export default function WordDiscover() {
    const [word, setWord] = useState("");
    const [vocabData, setVocabData] = useState<IVocabData | null>(null);
    const [error, setError] = useState("");
    const [loading, setLoading] = useState(false);
  
    const handleSearch = async (e: React.FormEvent) => {
      e.preventDefault();
      setLoading(true);
      setError("");
      setVocabData(null);
  
      try {
        // Replace with your actual route
        const res = await fetch("/api/vocabulary/word-discovery", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ word }),
        });
  
        if (!res.ok) {
          throw new Error("Failed to fetch vocabulary data");
        }
  
        const data: IVocabData = await res.json();
        console.log(data);
        setVocabData(data);
      } catch (err: any) {
        console.error(err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
  
    return (
      <main className=" flex flex-col items-center ">
        <div className="max-w-lg w-full bg-white rounded ">
          <h1 className="text-2xl font-semibold mb-4">Vocabulary Explorer</h1>
  
          {/* SEARCH FORM */}
          <form onSubmit={handleSearch} className="flex flex-col space-y-4">
            <label htmlFor="wordInput" className="font-medium">
              Enter a word:
            </label>
            <input
              id="wordInput"
              type="text"
              className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={word}
              onChange={(e) => setWord(e.target.value)}
              placeholder="e.g., 'serendipity'"
              required
            />
            <div className="grid grid-cols-2 gap-2 my-3" >
            <Button type="submit" disabled={loading}>
              {loading ? "Searching..." : "Search"}
            </Button> 
            <Button onClick={()=>setVocabData(null)} >
                Clear
            </Button>
            </div>

          </form>
  
          {/* ERROR MESSAGE */}
          {error && (
            <div className="mt-4 p-2 bg-red-100 border border-red-300 text-red-700">
              {error}
            </div>
          )}
  
          {/* SHOW RESULTS IF vocabData EXISTS */}
          {vocabData && (
            <div className="my-3" >
                <WordCard data={vocabData} />
            </div>  
          )}
        </div>
      </main>
    );
  }