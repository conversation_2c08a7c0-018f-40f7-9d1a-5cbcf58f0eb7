"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea"
import {
    Carousel,
    CarouselContent,
    CarouselItem,
    CarouselNext,
    CarouselPrevious,
  } from "@/components/ui/carousel"
  import { useManageTranslator } from "@/hooks/use-manage-translator";
  import TextSelectionActions from "./text-selection";
  import { basicActions, actions } from "@/lib/actions-text-selections";
import SmallDisplay from "./small-display";



interface ITranslation {
  text: string;
  ipa: string;
}

// The shape of the returned data
interface IResponseData {
  translations: Record<string, ITranslation[]>;
}

// A minimal “carousel” using ScrollArea
;


export default function TranslatorPage() {
  // Default the user’s chosen language to ["english"]


  const {
    selectedLang,
    setSelectedLang,
    inputText,
    setInputText,
    responseData,
    setResponseData,
    loading,
    setLoading,
    errorMsg,
    setErrorMsg,
    handleTranslate,
  } = useManageTranslator();

  console.log(responseData)

  // Example list of languages
  const languageOptions = [
    "english",
    "french",
    "german",
    "spanish",
    "italian",
    "chinese",
  ];


  return (
    <TextSelectionActions
            chat={true}
            translate={true}
            smallDiplay={true} 
            actions={
              [
                ...basicActions,            
                actions.handleSynonyms,
                actions.handleExamples
    
              ]
            } className="w-full  p-6 space-y-6 ">

      <h1 className="text-2xl font-bold">Flexible Translator</h1>

      {/* User input */}
      <div className="flex flex-col gap-2 w-full">
        <Textarea
          placeholder="Enter text to translate"
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
        />

        {/* Language selector (default to English) */}
        <Select
          onValueChange={(val) => setSelectedLang(val)}
          defaultValue={selectedLang}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select a language" />
          </SelectTrigger>
          <SelectContent>
            {languageOptions.map((lang) => (
              <SelectItem value={lang} key={lang}>
                {lang}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Translate button */}
        <Button onClick={handleTranslate} disabled={loading}>
          {loading ? "Translating..." : "Translate"}
        </Button>

        {errorMsg && <p className="text-red-500">{errorMsg}</p>}
      </div>

      {/* Display results in a carousel for each language */}
      {responseData && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Translations</h2>
            <Carousel className="rounded-md">
                <CarouselContent className="bg-white p-2 rounded-md" >
                    {
                        responseData.translations.map((item, idx) => (
                            <CarouselItem className=" p-2" key={idx}>
                                <div className="p-4 w-full bg-white rounded-lg   flex flex-col items-center justify-center">
                                    <p className="text-sm font-medium leading-none">{item.text}</p>
                                    <p className="text-sm text-muted-foreground">{item.ipa}</p>
                                </div>
                            </CarouselItem>
                        ))
                    }
                </CarouselContent>
                <CarouselPrevious className="-left-4" />
                <CarouselNext className="-right-4" />
            </Carousel>
        </div>
      )}
    </TextSelectionActions>
  );
}
