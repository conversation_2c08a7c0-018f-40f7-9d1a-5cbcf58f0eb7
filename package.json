{"name": "ipa-transcription", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@langchain/community": "^0.3.19", "@langchain/core": "^0.3.26", "@langchain/langgraph": "^0.2.34", "@langchain/mistralai": "^0.2.0", "@langchain/openai": "^0.3.16", "@prisma/client": "^6.11.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.1.6", "@tanstack/react-table": "^8.20.6", "@types/dompurify": "^3.2.0", "@types/html-to-text": "^9.0.4", "@types/prop-types": "^15.7.14", "ai": "^4.0.22", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.3", "drizzle": "^1.4.0", "embla-carousel-react": "^8.5.1", "fast-xml-parser": "^5.2.3", "hono": "^4.6.14", "html-to-text": "^9.0.5", "lucide-react": "^0.469.0", "marked": "^15.0.4", "next": "14.2.4", "openai": "^4.77.0", "p-limit": "^6.2.0", "prisma": "^6.1.0", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-quill": "^2.0.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/marked": "^6.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}