// app/page.tsx
"use client";

import React, { useState, useCallback, useEffect } from "react";
/* import TextSelectionAction from "@/components/text-selection"; */
/* import ReactQuill from "react-quill"; */
import 'react-quill/dist/quill.snow.css'; 
import { compiledConvert } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import dynamic from 'next/dynamic'
import Link from 'next/link'
import { useManageChat } from "@/hooks/use-manage-chat";
import { useIsMobile } from "@/hooks/use-mobile";


const TextSelectionAction = dynamic(() => import('@/components/text-selection'), {
  ssr: false,
  loading: () => <p>Loading editor...</p>,
})
const ReactQuill = dynamic(() => import("react-quill"), {
  ssr: false,
  loading: () => <p>Loading editor...</p>,
})

export default function Home() {
  const [text, setText] = useState("");
  const [ipa, setIpa] = useState("");
  const [loading, setLoading] = useState(false);
  const [readText,SetReadText] = useState("")
    const { isOpen, setIsOpen,toggleOpen } = useManageChat();
const isMobile = useIsMobile();


    useEffect(()=>{

        toggleOpen(false)     
      
    },[])


  const handleTextToSpeech = useCallback(() => {
    
        const utterance = new SpeechSynthesisUtterance(readText)
        window.speechSynthesis.speak(utterance)    
      }, [readText])
  

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    setIpa("");
    
    
    const formated = compiledConvert(text)
    SetReadText(formated)

    try {
      const res = await fetch("/api/ipa", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      body: JSON.stringify({ text: formated }),
      });

      if (!res.ok) {
        throw new Error("Failed to fetch IPA transcription");
      }

      const data = await res.json();
      setIpa(data.ipa);
    } catch (error) {
      console.error("Error:", error);
      setIpa("An error occurred while fetching the IPA transcription.");
    } finally {
      setLoading(false);
    }
  };

  return (

    <main className="flex h-full flex-col" >
        <div className="flex-1   flex items-center justify-center " >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 w-[50%]">
            <Button>
            <Link href="/vocabulary/ipa">
                IPA
            </Link>
            </Button>
            <Button>
            <Link href="/vocabulary/theme-discover">
              Theme Discover
            </Link>
            </Button>
            <Button>
            <Link href="/vocabulary/word-discover">
              Word Discover
            </Link>
            </Button>
            <Button>
            <Link href="/chat">
              Chat
            </Link>
            </Button>
            <Button>
            <Link href="/translator">
              Translation
              </Link>
            </Button>
            <Button>
            <Link href="/reader">
              reader
              </Link>
            </Button>
            <Button>
            <Link href="/vocabulary/mnemonic">
              Mnemonic Generator
            </Link>
            </Button>
            <Button>
            <Link href="/vocabulary/ipa-to-word">
              IPA to Word
            </Link>
            </Button>
          </div>
        </div>
    </main>

  );
}

