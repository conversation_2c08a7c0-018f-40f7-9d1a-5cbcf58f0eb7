// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model NewThemeDiscover {
  id            String   @id @default(auto()) @map("_id")  @db.ObjectId
  theme         String
  subCategory   String[]

  nouns         Json
  verbs         Json
  adjectives    Json
  adverbs       Json

  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model CustomChatSetting {
  id                     String              @id @default(auto()) @map("_id") @db.ObjectId
  type                   String              // "topic" or "grammarRule"
  value                  String              @unique
  weight                 Int                 @default(10)
  createdAt              DateTime            @default(now())
  updatedAt              DateTime            @updatedAt

  tagIds                 String[]            @db.ObjectId
  tags                   Tag[]               @relation("GrammarRuleTags", fields: [tagIds], references: [id])

  grammarRuleSections    GrammarRuleSection[]
  chatSectionTopics      ChatSectionTopic[]
}

model Tag {
  id              String               @id @default(auto()) @map("_id") @db.ObjectId
  name            String               @unique
  description     String?              // Optional description field for tags
  createdAt       DateTime             @default(now())
  updatedAt       DateTime             @updatedAt

  grammarRuleIds  String[]             @db.ObjectId
  grammarRules    CustomChatSetting[]  @relation("GrammarRuleTags", fields: [grammarRuleIds], references: [id])
}

model ChatSection {
  id                  String               @id @default(auto()) @map("_id") @db.ObjectId
  content             String
  grammarRuleCount    Int                  @default(1) // Number of grammar rules to select
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt

  grammarRuleSections GrammarRuleSection[]
  messages            Message[]
  chatSectionTopics   ChatSectionTopic[]
}

model GrammarRuleSection {
  id            String            @id @default(auto()) @map("_id") @db.ObjectId
  chatSectionId String            @db.ObjectId
  grammarRuleId String            @db.ObjectId
  weight        Int

  chatSection   ChatSection       @relation(fields: [chatSectionId], references: [id])
  grammarRule   CustomChatSetting @relation(fields: [grammarRuleId], references: [id])

  @@unique([chatSectionId, grammarRuleId])
}

model Message {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  chatSectionId String    @db.ObjectId
  role          String
  content       String
  correction    Json?
  suggestion    Json?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  chatSection   ChatSection @relation(fields: [chatSectionId], references: [id])
}

model ChatSectionTopic {
  id                    String             @id @default(auto()) @map("_id") @db.ObjectId
  chatSectionId         String             @db.ObjectId
  customChatSettingId   String             @db.ObjectId
  createdAt             DateTime           @default(now())

  chatSection           ChatSection        @relation(fields: [chatSectionId], references: [id])
  customChatSetting     CustomChatSetting  @relation(fields: [customChatSettingId], references: [id])

  @@unique([chatSectionId, customChatSettingId])
}
