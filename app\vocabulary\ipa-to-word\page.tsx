"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { TextSelectionActions } from "@/components/text-selection";
import { basicActions, actions } from "@/lib/actions-text-selections";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

type Match = {
  word: string;
  ipa: string;
  visualDescription: string;
  language: string;
};

const languageOptions = [
  { value: "English", label: "English" },
  { value: "French", label: "French" },
  { value: "Spanish", label: "Spanish" },
  { value: "German", label: "German" },
  { value: "Italian", label: "Italian" },
  { value: "Japanese", label: "Japanese" },
  { value: "Chinese", label: "Chinese" },
];

export default function IPAToWordPage() {
  const [ipaInput, setIpaInput] = useState("");
  const [selectedLanguages, setSelectedLanguages] = useState<string[]>(["English"]);
  const [partOfSpeech, setPartOfSpeech] = useState("any");
  const [customQuery, setCustomQuery] = useState("");
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [loading, setLoading] = useState(false);
  const [matches, setMatches] = useState<Match[]>([]);
  const [error, setError] = useState("");

  const handleAddLanguage = (language: string) => {
    if (!selectedLanguages.includes(language)) {
      setSelectedLanguages([...selectedLanguages, language]);
    }
  };

  const handleRemoveLanguage = (language: string) => {
    if (selectedLanguages.length > 1) {
      setSelectedLanguages(selectedLanguages.filter(lang => lang !== language));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMatches([]);
    setError("");

    try {
      const res = await fetch("/api/ipa-to-word", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          ipaInput, 
          languages: selectedLanguages, 
          partOfSpeech: showAdvanced ? partOfSpeech : "any",
          customQuery: showAdvanced ? customQuery : ""
        }),
      });

      if (!res.ok) {
        throw new Error("Failed to fetch word matches");
      }

      const data = await res.json();
      
      if (data.error) {
        setError(data.error);
      } else if (Array.isArray(data.matches)) {
        setMatches(data.matches);
      } else {
        setError("Received invalid response format");
      }
    } catch (error) {
      console.error("Error:", error);
      setError("An error occurred while processing your request");
    } finally {
      setLoading(false);
    }
  };

  return (
    <main className="flex min-h-screen flex-col items-center p-8">
      <div className="max-w-3xl w-full">
        <h1 className="text-3xl font-bold mb-6">IPA to Word Matcher</h1>
        <p className="mb-6 text-gray-600">
          Enter an IPA phonetic spelling and find words or phrases that contain or sound similar to that pattern.
        </p>

        <form onSubmit={handleSubmit} className="space-y-4 mb-8">
          <div className="flex flex-col space-y-2">
            <label htmlFor="ipaInput" className="font-medium">
              IPA Phonetic Pattern:
            </label>
            <Input
              id="ipaInput"
              value={ipaInput}
              onChange={(e) => setIpaInput(e.target.value)}
              placeholder="e.g. /ˈæpəl/, /kæt/, /ˈwɔtər/"
              className="w-full"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label className="font-medium">Target Languages:</label>
            <div className="flex flex-wrap gap-2 mb-2">
              {selectedLanguages.map(lang => (
                <Badge key={lang} variant="secondary" className="px-2 py-1">
                  {lang}
                  <button 
                    type="button" 
                    onClick={() => handleRemoveLanguage(lang)}
                    className="ml-1 text-gray-500 hover:text-gray-700"
                  >
                    <X size={14} />
                  </button>
                </Badge>
              ))}
            </div>
            <Select onValueChange={handleAddLanguage}>
              <SelectTrigger>
                <SelectValue placeholder="Add a language" />
              </SelectTrigger>
              <SelectContent>
                {languageOptions.map(option => (
                  <SelectItem 
                    key={option.value} 
                    value={option.value}
                    disabled={selectedLanguages.includes(option.value)}
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2 pt-2">
            <Switch 
              id="advanced-options" 
              checked={showAdvanced} 
              onCheckedChange={setShowAdvanced} 
            />
            <Label htmlFor="advanced-options">Show advanced options</Label>
          </div>

          {showAdvanced && (
            <div className="space-y-4 p-4 border rounded-md">
              <div className="flex flex-col space-y-2">
                <label htmlFor="partOfSpeech" className="font-medium">
                  Part of Speech:
                </label>
                <Select value={partOfSpeech} onValueChange={setPartOfSpeech}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select part of speech" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any">Any</SelectItem>
                    <SelectItem value="nouns">Nouns</SelectItem>
                    <SelectItem value="verbs">Verbs</SelectItem>
                    <SelectItem value="adjectives">Adjectives</SelectItem>
                    <SelectItem value="adverbs">Adverbs</SelectItem>
                    <SelectItem value="prepositions">Prepositions</SelectItem>
                    <SelectItem value="conjunctions">Conjunctions</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex flex-col space-y-2">
                <label htmlFor="customQuery" className="font-medium">
                  Custom Query (Optional):
                </label>
                <Textarea
                  id="customQuery"
                  value={customQuery}
                  onChange={(e) => setCustomQuery(e.target.value)}
                  placeholder="E.g., 'words related to nature' or 'words that evoke happiness'"
                  className="w-full"
                />
              </div>
            </div>
          )}

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? "Finding matches..." : "Find Matching Words"}
          </Button>
        </form>

        {error && (
          <div className="p-4 mb-6 bg-red-50 text-red-600 rounded-md">
            {error}
          </div>
        )}

        {matches.length > 0 && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Matching Words & Phrases</h2>
            <TextSelectionActions
              chat={true}
              translate={true}
              smallDiplay={true} 
              actions={
                [
                  ...basicActions,            
                  actions.handleSynonyms,
                  actions.handleExamples,
                  actions.handleIPA,
                  actions.handleAntonyms
                ]
              }>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {matches.map((match, index) => (
                  <Card key={index}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{match.word}</CardTitle>
                          <CardDescription className="text-md font-mono">{match.ipa}</CardDescription>
                        </div>
                        <Badge>{match.language}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p>{match.visualDescription}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TextSelectionActions>
          </div>
        )}
      </div>
    </main>
  );
}


