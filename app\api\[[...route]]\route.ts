import { Hono } from "hono"
import { handle } from "hono/vercel"
import chat from "./chat"
import essay from "./essay"
import ipa from "./ipa"
import vocabulary from "./vocabulary"
import mnemonic from "./mnemonic"
import ipaToWord from "./ipa-to-word"
import customSettings from "./custom-settings"
import chatSections from "./chat-sections"

export const runtime = "nodejs"

const app = new Hono().basePath("/api")

const routes = app
    .route("/chat", chat)
    .route("/essay", essay)
    .route("/ipa", ipa)
    .route("/vocabulary", vocabulary)
    .route("/mnemonic", mnemonic)
    .route("/ipa-to-word", ipaToWord)
    .route("/custom-settings", customSettings)
    .route("/chat-sections", chatSections)

export const GET = handle(app);
export const POST = handle(app);
export const PATCH = handle(app);
export const DELETE = handle(app);
