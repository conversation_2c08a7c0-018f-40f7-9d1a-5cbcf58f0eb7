
import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { useManageChat } from "@/hooks/use-manage-chat";
import "./globals.css";
import { Button } from "@/components/ui/button";
import { <PERSON>t<PERSON>orm , ChatManager } from "@/components/chat";
import LayoutWrapper from "@/components/layout-wrapper";
import { Toaster } from "@/components/ui/toaster"
import { TooltipProvider } from "@/components/ui/tooltip"

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {


  return (
    <html lang="en">
      <body
        className={`${inter.className} antialiased flex  items-center justify-center`}
      >
        <TooltipProvider>
          <LayoutWrapper>
            {children}
          </LayoutWrapper>
          <Toaster />
        </TooltipProvider>
      </body>
    </html>
  );
}



