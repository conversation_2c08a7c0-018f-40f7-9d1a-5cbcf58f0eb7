import { useCallback, useEffect, useState  } from "react";
import { set } from "zod";

 export const useTextSelection = ()=>{
    const [text,setText] = useState<string>('')
    const [hasSelection, setHasSelection] = useState<boolean>(false)
    const [position, setPosition] = useState({ x: 0, y: 0 })



    const handleSelectionChange = useCallback(()=>{
        const selection = window.getSelection()

        if(!selection) return 

        const selectedText = selection.toString()

        if( selectedText.trim().length > 0 ){

            const range = selection.getRangeAt(0)
            const rect = range.getBoundingClientRect()
            setPosition({
                x:rect.left + rect.width / 2,
                y:rect.top -10
            })

            	setText(selectedText)
                setHasSelection(true)
        }else{
                setHasSelection(false)
                setText("")
        }

    },[])

    useEffect(()=>{
        document.addEventListener(`selectionchange`,handleSelectionChange)

        return ()=>{
            document.removeEventListener(`selectionchange`, handleSelectionChange)
        }
    },[handleSelectionChange])


    const clearSelection = useCallback(()=>{
        setHasSelection(false)
        setText("")
    },[])


    return{
        text,
        hasSelection,
        position,
        clearSelection
    }
} 

