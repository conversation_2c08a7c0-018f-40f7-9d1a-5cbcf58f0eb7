"use client";

import React from "react";
import { useManageChat } from "@/hooks/use-manage-chat";
import { ChatManager } from "@/components/chat";
import SmallDisplay from "./small-display";

type Props = {
  children: React.ReactNode;
};

const LayoutWrapper = ({ children }: Props) => {
  const { isOpen } = useManageChat();

  return (
    <div className="flex w-full h-svh overflow-y-hidden">
      <div
        className={`transition-all h-full overflow-y-auto duration-300 ${
          isOpen ? "w-[70%]" : "w-full"
        }`}
      >
        <div className="relative">
          {children}
        </div>
      </div>
      {isOpen && (
        <div className="relative w-[30%] h-full flex flex-col transition-all duration-300 overflow-y-hidden">
          <div>
            <SmallDisplay />
          </div>
          <ChatManager />
        </div>
      )}
    </div>
  );
};

export default LayoutWrapper;
