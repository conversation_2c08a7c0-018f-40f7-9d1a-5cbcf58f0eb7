import { Actions } from "@/components/text-selection";
import {
    Volume2,
    <PERSON><PERSON>,
    Search,
    BookOpen,
    ThumbsDown,
    Pencil,
    Mic2, // New icon for IPA
} from "lucide-react";

type Props = {
    selectedText: string, 
    input?:string, 
    setResult?:(input:string)=>void,
    setIsLoading?:(input:boolean)=>void,

}

const fetchFromLLM = async ({instruction, selectedText, setResult, setIsLoading}:{instruction: string, selectedText: string, setResult?: (input: string) => void, setIsLoading ? : (input:boolean)=>void}) => {
    console.log(selectedText)
    try {
        setIsLoading && setIsLoading(true)
        const response = await fetch('/api/chat/multifunc', { // Updated endpoint
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                input:selectedText,
                instruction: `${instruction}\n\nPlease format the output properly and provide the IPA spelling of the output words.`,
            }),
        });

        if (!response.ok) {
            throw new Error('LLM request failed.');
        }

        const data = await response.json();
        console.log("LLM response:", data);
        setResult && setResult(data.text);
        setIsLoading && setIsLoading(false)

    } catch (error) {
        
        console.error("Error with LLM request:", error);
        setResult && setResult("Failed to fetch data from LLM.");
        setIsLoading && setIsLoading(false)

    }
};

// Synonyms Handler
export const handleSynonyms = async ({ selectedText, setResult, setIsLoading }: Props) => {
    console.log(setResult)
    console.log(selectedText)
    const instruction = 'Find synonyms for the following text:';
    await fetchFromLLM({instruction, selectedText, setResult, setIsLoading});
};

// Antonyms Handler
export const handleAntonyms = async ({ selectedText, setResult, setIsLoading }: Props) => {
    const instruction = 'Find antonyms for the following text:';
    await fetchFromLLM({instruction, selectedText, setResult, setIsLoading});
};

// Example Sentences Handler
export const handleExamples = async ({ selectedText, setResult, setIsLoading }: Props) => {
    const instruction = 'Provide example sentences for the following text:';
    await fetchFromLLM({instruction, selectedText, setResult, setIsLoading});
};

// IPA Handler (to be added in Step 3)
export const handleIPA = async ({ selectedText, setResult, setIsLoading }: Props) => {
    const instruction = 'Provide the IPA (International Phonetic Alphabet) spelling for the following text:';
    await fetchFromLLM({instruction, selectedText, setResult});
};

export const handleSpeak = ({ selectedText }: { selectedText: string }): void => {
    const utterance = new SpeechSynthesisUtterance(selectedText);
  
    // Set language to English
    utterance.lang = 'en';
  
    // Optionally adjust other settings (like pitch, rate, or volume)
    utterance.rate = 1;  // Normal speed
    utterance.pitch = 1; // Normal pitch
    utterance.volume = 1; // Full volume
  
    window.speechSynthesis.speak(utterance);
  };

  export const handleCopy = ({selectedText}:Props) => {
    navigator.clipboard.writeText(selectedText);
    console.log("Copied text:", selectedText);
  };

  export const actions = {
    handleSynonyms:{
        label: "Find Synonyms",
        icon: BookOpen, // Choose an appropriate icon from Lucide
        llm: true,
        onClick: handleSynonyms,
    },
    handleAntonyms:{
        label: "Find Antonyms",
        icon: ThumbsDown, // Choose an appropriate icon from Lucide
        llm: true,
        onClick: handleAntonyms,
    },
    handleExamples:{
        label: "Example Sentences",
        icon: Pencil, // Choose an appropriate icon from Lucide
        llm: true,
        onClick: handleExamples,
    },
    handleIPA:{
        label: "IPA Spelling", // New action
        icon: Mic2, // Choose an appropriate icon from Lucide (e.g., Mic2)
        llm: true,
        onClick: handleIPA,
    },
  }

  export const basicActions : Actions = [
        {
        label: "Speak",
        icon: Volume2,
        notClose:true,
        onClick: handleSpeak,
      },
      {
        label: "Copy",
        icon: Copy,
        onClick: handleCopy,
      },
  ]