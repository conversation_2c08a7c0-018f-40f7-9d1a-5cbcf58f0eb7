import { Hono } from "hono";
import {
  HumanMessage,
  SystemMessage,
  BaseMessage,
} from "@langchain/core/messages";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { Annotation } from "@langchain/langgraph";
import { sp_TV } from "@/lib/prompt";
import { tool } from "@langchain/core/tools";
import { ChatOpenAI } from "@langchain/openai";
import { ChatTogetherAI } from "@langchain/community/chat_models/togetherai";
import { Agent } from "@/lib/ai-functions";
import { z } from "zod";
import { schemaThemes } from "@/lib/schemas";
import { ChatMistralAI } from "@langchain/mistralai";

const schema = z.object({
  themes: z
    .array(
      z.object({
        theme: z.string(),
        description: z.string(),
      })
    )
    .describe("array of themes proposition"),
});

const searchtool = tool(
  (input) => {
    return "search result";
  },
  {
    name: "searchtool",
    description: "search tool",
    schema: z.string().describe("search to be made"),
  }
);

const app = new Hono()
  .post("/correction-essay", async (c) => {
    const openai = new ChatOpenAI({
      model: "gpt-4o",
    }); /* .withStructuredOutput(schemaThemes) */

    const togetherai = new ChatTogetherAI({
      model: "deepseek-ai/DeepSeek-V3",
      temperature: 0,
    }); /* .withStructuredOutput(schemaThemes) */

    const mistralai = new ChatMistralAI({
      model: "mistral-large-latest",
      temperature: 0,
    }); /* .withStructuredOutput(schemaThemes) */

    const AgentState = Annotation.Root({
      messages: Annotation<BaseMessage[]>({
        reducer: (x, y) => x.concat(y),
        default: () => [
          new SystemMessage(`you help people learn languages be cool sir`),
        ],
      }),
    });

    const preAgent = new Agent({
      model: togetherai,
      State: AgentState,
      tools: [searchtool],
    });
    const agent = preAgent.create();

    let success = false;
    let attempts = 0;

    while (!success && attempts < 5) {
 
        attempts += 1;

            try {
      const { topic, simulation } = await c.req.json<{
        topic: string;
        simulation?: boolean;
      }>();
      console.log("Received topic:", topic);
      console.log("Simulation mode:", simulation);

      const AgentState = Annotation.Root({
        messages: Annotation<BaseMessage[]>({
          reducer: (x, y) => x.concat(y),
          default: () => [
            new SystemMessage(simulation ? `` : `correct and suggest ideas`),
          ],
        }),
      });

      const preAgent = new Agent({
        model: togetherai,
        State: AgentState,
        tools: [searchtool],
      });
      const agent = preAgent.create();

      const newHMessage = new HumanMessage(topic);

      const response = await agent.invoke(
        { messages: [newHMessage] },
        { configurable: { thread_id: "8" } }
      );
      const lastMessage = response.messages[response.messages.length - 1];
      const parsedResponse = await new StringOutputParser().invoke(lastMessage);

      console.log(parsedResponse);

      return c.json({ text: parsedResponse });
    } catch (error: any) {
      console.error("Error in vocabulary route:", error);
      
    }

    }


    
    return c.json({ error: "Failed to process essay correction." }, 500);
  })
  .post("/theme-essay", async (c) => {
    const openai = new ChatOpenAI({
      model: "gpt-4o-mini",
    }).withStructuredOutput(schema);

    const togetherai = new ChatTogetherAI({
      model: "deepseek-ai/DeepSeek-V3",
      temperature: 0,
    }).withStructuredOutput(schema);

    const mistralai = new ChatMistralAI({
      model: "mistral-large-latest",
      temperature: 0,
    }).withStructuredOutput(schema);

    // Parse the JSON body
    const { topic, simulation } = await c.req.json();
    console.log("Received topic:", topic);
    console.log("Simulation mode:", simulation);

    // Define the AgentState with dynamic SystemMessage based on simulation
    const AgentState = Annotation.Root({
      messages: Annotation<BaseMessage[]>({
        reducer: (x, y) => x.concat(y),
        default: () => [
          new SystemMessage(
            simulation
              ? ``
              : `i want to learn the vocabulary of the topic: ${topic} give me some ideas of essays writting i could write which will help me go deep in the vocabulary`
          ),
        ],
      }),
    });

    // Initialize a new Agent with the dynamic state
    const preAgent = new Agent({
      model: togetherai,
      State: AgentState,
      tools: [searchtool],
    });
    const agent = preAgent.create();

    const newHMessage = new HumanMessage(topic);

    let success = false;
    let attempts = 0;

    while (!success && attempts < 5) {
      try {
        attempts += 1;
        // Invoke the agent with the human message
        const response = await agent.invoke(
          { messages: [newHMessage] },
          { configurable: { thread_id: "8" } }
        );

        // Extract the last message from the response
        const lastMessage = response.messages[response.messages.length - 1];
        console.log("Last Message:", lastMessage);

        // Return the last message as JSON (parsed if needed)
        return c.json({ themes: lastMessage });
      } catch (error: any) {
        console.error("Error in /api/vocabulary/theme-discover route:", error);
        // Return a 500 error with a JSON error message
      }
    }

    return c.json({ error: "Failed to process theme discovery." }, 500);
  });

export default app;
