import { mnemonic, newThemeDiscover } from "@/lib/schemas";
import {z} from "zod"
import { Badge } from "@/components/ui/badge"
import CardDisplay from "@/components/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

type Props = z.infer<typeof newThemeDiscover>

const DisplayWord = ({type,vocabList}:{type:"nouns" | "verbs" | "adverbs" | "adjectives",vocabList:Props | undefined }) => {

  if(!vocabList){
    return undefined
  }

    const rightItem = getRightItem(type,vocabList)

      if(!rightItem){
        return (<div>something when wrong</div>)
      }

        return(
        (typeof vocabList.nouns === "string" || typeof vocabList.verbs === "string" || typeof vocabList.adverbs === "string" || typeof vocabList.adjectives === "string" ? 
            (<div>something when wrong</div>)
                :
            (
              <div className="flex flex-col gap-4" >
                <div className="flex flex-col gap-2">
                  <div><Badge className="text-[15px] my-2" variant="outline">Hight Frequency</Badge></div>
                  <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-3" >
                    {
                        rightItem.high
                    }
                  </div>
                  <div>
                    <div>
                      <Accordion type="single" collapsible>
                        <AccordionItem value="item-1">
                          <AccordionTrigger>The Keyword Method</AccordionTrigger>
                          <AccordionContent>
                          
                            {
                              rightItem.mnemonicH.keywordMethod.map((m) => (
                                  <Badge className="text-[15px] my-2 " variant="outline">{m}</Badge>
                                ))
                              }
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    </div>
                    <div>
                    <Accordion type="single" collapsible>
                        <AccordionItem value="item-1">
                          <AccordionTrigger>Imagination Visualisation</AccordionTrigger>
                          <AccordionContent>
                            {
                              rightItem.mnemonicH.associationAndVisualization.map((m) => (
                                <Badge className="text-[15px] my-2 " variant="outline">{m}</Badge>
                              ))
                            }
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    </div>
                  </div> 
                </div>
                <div className="flex flex-col gap-2  ">
                  <div><Badge className="text-[15px] my-2 " variant="outline">Middle Frequency</Badge></div>
                  <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-3" >
                    {
                        rightItem.middle
                    }
                  </div>
                  <div>
                    <div>
                      <Accordion type="single" collapsible>
                        <AccordionItem value="item-1">
                          <AccordionTrigger>The Keyword Method</AccordionTrigger>
                          <AccordionContent>
                          
                            {
                              rightItem.mnemonicM.keywordMethod.map((m) => (
                                  <Badge className="text-[15px] my-2 " variant="outline">{m}</Badge>
                                ))
                              }
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    </div>
                    <div>
                    <Accordion type="single" collapsible>
                        <AccordionItem value="item-1">
                          <AccordionTrigger>Imagination Visualisation</AccordionTrigger>
                          <AccordionContent>
                            {
                              rightItem.mnemonicM.associationAndVisualization.map((m) => (
                                <Badge className="text-[15px] my-2 " variant="outline">{m}</Badge>
                              ))
                            }
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    </div>
                  </div>
                </div>
              </div>
              
            )
        )
    )
}


export default DisplayWord

function getRightItem(type: string, vocabList: Props) {
    switch (type) {
      case "nouns":
        
        if(typeof vocabList.nouns === "string") return undefined

        return ({
          high:vocabList.nouns.nouns.usage.hightFrequency.map((m) => (
          <CardDisplay   m={m} />
        )),
          mnemonicH:vocabList.nouns.mnemonic.hightFrequency,
          mnemonicM:vocabList.nouns.mnemonic.middleFrequency,
          middle:vocabList.nouns.nouns.usage.middleFrequency.map((m) => (
          <CardDisplay  m={m} />
        ))
      });
      case "verbs":
        if(typeof vocabList.verbs === "string") return undefined
        return {
          high:vocabList.verbs.verbs.usage.hightFrequency.map((m) => (
          <CardDisplay  m={m} />
        )),
          mnemonicH:vocabList.verbs.mnemonic.hightFrequency,
          mnemonicM:vocabList.verbs.mnemonic.middleFrequency,
          middle:vocabList.verbs.verbs.usage.middleFrequency.map((m) => (
          <CardDisplay  m={m} />
        ))
      };
      case "adverbs":
        if(typeof vocabList.adverbs === "string") return undefined
        return {
          high:vocabList.adverbs.adverbs.usage.hightFrequency.map((m) => (
          <CardDisplay m={m} />
        )),
          mnemonicH:vocabList.adverbs.mnemonic.hightFrequency,
          mnemonicM:vocabList.adverbs.mnemonic.middleFrequency,
          middle:vocabList.adverbs.adverbs.usage.middleFrequency.map((m) => (
          <CardDisplay  m={m} />
        ))
      };
      case "adjectives":
        if(typeof vocabList.adjectives === "string") return undefined
        return {
          high:vocabList.adjectives.adjectives.usage.hightFrequency.map((m) => (
          <CardDisplay  m={m} />
        )),
          mnemonicH:vocabList.adjectives.mnemonic.hightFrequency,
          mnemonicM:vocabList.adjectives.mnemonic.middleFrequency,
          middle:vocabList.adjectives.adjectives.usage.middleFrequency.map((m) => (
          <CardDisplay  m={m} />
        ))
      };
      default:
        return null; // or handle the default case as needed
    }
  }
  