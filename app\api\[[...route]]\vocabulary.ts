// app/api/vocabulary/group.ts

import { Hono } from "hono";
import { Context } from "hono";
import {
  HumanMessage,
  SystemMessage,
  BaseMessage,
} from "@langchain/core/messages";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { Annotation } from "@langchain/langgraph";
import { tool } from "@langchain/core/tools";
import { ChatOpenAI } from "@langchain/openai";
import { ChatTogetherAI } from "@langchain/community/chat_models/togetherai";
import { ChatMistralAI } from "@langchain/mistralai";
import { Agent } from "@/lib/ai-functions";
import { themeAgent } from "@/lib/ai-functions-class";
import { z } from "zod";
import { schemaThemeDiscover, schemaDiscovery } from "@/lib/schemas";
import prisma from "@/lib/prisma";
import pLimit from "p-limit";
import { sp_WD } from "@/lib/prompt";

// Initialize Hono group for vocabulary
const app = new Hono();

const searchtool = tool(
  (input) => {
    return "search result";
  },
  {
    name: "searchtool",
    description: "search tool",
    schema: z.string().describe("search to be made"),
  }
);

// Define your search tool using LangChain
const searchTool = tool(
  (input: string) => {
    // Implement actual search logic here if needed
    return "search result";
  },
  {
    name: "searchtool",
    description: "A tool to perform searches based on user input.",
    schema: z.string().describe("The search query to be executed."),
  }
);

// Define the Agent state with initial system message

// Initialize the Agent with tools and state

// Main POST route handler for /api/vocabulary
app.post("/theme-vocab", async (c: Context) => {
  const limit = pLimit(2);
  // Initialize AI models with structured output
  const openai = new ChatOpenAI({
    model: "gpt-4o-mini", // Ensure the model name is correct
  }).withStructuredOutput(schemaThemeDiscover);

  const togetherai = new ChatTogetherAI({
    model: "deepseek-ai/DeepSeek-V3",
    temperature: 0,
  }).withStructuredOutput(schemaThemeDiscover);

  const mistralai = new ChatMistralAI({
    model: "mistral-large-latest",
    temperature: 0,
  }).withStructuredOutput(schemaThemeDiscover);

  let success = false;
  let attempts = 0;

  while (!success && attempts < 5) {
    try {
      // Parse the JSON body
      const { input } = await c.req.json();
      console.log("Received theme:", input);

      // Invoke the themeAgent with the provided theme
      const response = await themeAgent.invoke({ theme: input });
      console.log("Initial Response:", response);

      await prisma.newThemeDiscover.create({
        data: {
          theme: response.theme,
          subCategory: typeof response.subCategory ==="string" ? [response.subCategory] : response.subCategory,
          nouns: response.nouns,
          adverbs: response.adverbs,
          adjectives: response.adjectives,
          verbs: response.verbs,
        },
      });

      // Initialize an array to hold sub-category responses
      let subCategoryResponses: any[] = [];
      let subsubCategoryResponses: any[] = [];

      // Check if subCategory is an array
      if (response.subCategory && Array.isArray(response.subCategory)) {
        // Map over each subCategory and invoke themeAgent
        subCategoryResponses = await Promise.all(
          response.subCategory.map((category: string) =>
            limit(async () => {
              try {
                const subResponse = await themeAgent.invoke({
                  theme: category,
                });
                console.log(`SubCategory (${category}) Response:`, subResponse);

                await prisma.newThemeDiscover.create({
                  data: {
                    theme: subResponse.theme,
                    subCategory: typeof response.subCategory ==="string" ? [response.subCategory] : response.subCategory,
                    nouns: subResponse.nouns,
                    adverbs: subResponse.adverbs,
                    adjectives: subResponse.adjectives,
                    verbs: subResponse.verbs,
                  },
                });

                return subResponse;
              } catch (subError: any) {
                console.error(
                  `Error processing subCategory (${category}):`,
                  subError
                );
                return { error: `Failed to process subCategory: ${category}` };
              }
            })
          )
        );
      }

      // Aggregate the responses
      // Return the aggregated response as JSON
      return c.json(response);
    } catch (error: any) {
      console.error("Error in /api/vocabulary route:", error);
      // Return a 500 error with a JSON error message
    }
  }

  return c.json({ error: "Failed to fetch vocabulary data." }, 500);
});

app.post("/create-theme", async (c: Context) => {
  // Initialize AI models with structured output
  const openai = new ChatOpenAI({
    model: "gpt-4o-mini", // Ensure the model name is correct
  }).withStructuredOutput(schemaThemeDiscover);

  const togetherai = new ChatTogetherAI({
    model: "deepseek-ai/DeepSeek-V3",
    temperature: 0,
  }).withStructuredOutput(schemaThemeDiscover);

  const mistralai = new ChatMistralAI({
    model: "mistral-large-latest",
    temperature: 0,
  }).withStructuredOutput(schemaThemeDiscover);

  let success = false;
  let attempts = 0;

  while (!success && attempts < 5) {
    try {
      attempts += 1;
      // Parse the JSON body
      const { theme } = await c.req.json();
      console.log("Received theme:", theme);

      // Invoke the themeAgent with the provided theme
      const response = await themeAgent.invoke({ theme: theme });
      console.log("Initial Response:", response);
      /*     

/*     await prisma.newThemeDiscover.create({
    data:response
  }) */

      // Return the aggregated response as JSON
      return c.json(response);
    } catch (error: any) {
      console.error("Error in /api/vocabulary route:", error);
      // Return a 500 error with a JSON error message
    }
  }

  return c.json({ error: "Failed to fetch vocabulary data." }, 500);
});

// Add the new translate POST route
app.post("/word-discovery", async (c) => {
  const AgentState = Annotation.Root({
    messages: Annotation<BaseMessage[]>({
      reducer: (x, y) => x.concat(y),
      default: () => [
        new SystemMessage(`You are a helpful language assistant.  
  Given the user-provided word, return the following french traduction`),
      ],
    }),
  });

  const openai = new ChatOpenAI({
    model: "gpt-4o-mini",
  }).withStructuredOutput(schemaDiscovery);

  const togetherai = new ChatTogetherAI({
    model: "deepseek-ai/DeepSeek-V3",
    temperature: 0,
  }).withStructuredOutput(schemaDiscovery);

  const mistralai = new ChatMistralAI({
    model: "mistral-large-latest",
    temperature: 0,
  }).withStructuredOutput(schemaDiscovery);

  const preAgent = new Agent({
    model: togetherai,
    State: AgentState,
    tools: [searchtool],
  });
  const agent = preAgent.create();

  let success = false;
  let attempts = 0;

  while (!success && attempts < 5) {
    try {
      attempts += 1;
      // Parse the JSON body
      const { word } = await c.req.json<{ word: string }>();
      console.log("Received word:", word);

      // Invoke the themeAgent with the provided word
      const newHMessage = new HumanMessage(word);

      const response = await agent.invoke(
        { messages: [newHMessage] },
        { configurable: { thread_id: "7" } }
      );
      const lastMessage = response.messages[response.messages.length - 1];

      console.log(lastMessage);

      // Return the aggregated response as JSON
      return c.json(lastMessage);
    } catch (error: any) {
      console.error("Error in vocabulary route:", error);
      // Return a 500 error with a JSON error message
    }
  }

  return c.json({ error: "Failed to fetch vocabulary data." }, { status: 500 });
});

app.get("/all-themes", async (c) => {
  try {
    // Fetch all records from the `newThemeDiscover` table
    const basicThemes = await prisma.newThemeDiscover.findMany({
      select: {
        id: true,
        theme: true,
        subCategory: true,
      },
    });

    // Process themes with proper async handling
    const themes = await Promise.all(
      basicThemes.map(async (theme) => {
        return {
          theme: theme.theme,
          id: theme.id,
          subCategory: await Promise.all(
            theme.subCategory.map(async (subCat) => {
              const themeRecord = await prisma.newThemeDiscover.findFirst({
                where: {
                  theme: subCat,
                },
                select: {
                  theme: true,
                  id: true,
                },
              });

              if (!themeRecord) {
                return {
                  theme: subCat,
                  id: null,
                };
              } else {
                return {
                  theme: subCat,
                  id: themeRecord.id,
                };
              }
            })
          ),
        };
      })
    );

    // Flatten the array if necessary

    // Send the response to the client
    return c.json({ data: themes });
  } catch (error) {
    console.error(error);
    return c.json({ error: "Internal Server Error" }, 500);
  }
});

app.get("/theme/:id", async (c) => {
  const { id } = c.req.param();

  try {
    const themeRecord = await prisma.newThemeDiscover.findUnique({
      where: { id },
    });

    if (!themeRecord) {
      return c.json(
        {
          success: false,
          error: "Theme not found.",
        },
        404
      );
    }

    return c.json({
      data: themeRecord,
    });
  } catch (error) {
    console.error("Error fetching theme by ID:", error);

    return c.json(
      {
        success: false,
        error: "Failed to fetch the theme from the database.",
      },
      500
    );
  }
});

app.get("/theme-name", async (c) => {
  const { name } = c.req.query();
  console.log(name);
  if (!name) {
    return c.json(
      {
        success: false,
        error: "Theme name is required.",
      },
      400
    );
  }

  try {
    const themeRecord = await prisma.newThemeDiscover.findFirst({
      where: { theme: name },
    });

    console.log(themeRecord);

    if (!themeRecord) {
      return c.json(
        {
          success: false,
          error: "Theme not found.",
        },
        404
      );
    }

    return c.json({
      data: themeRecord,
    });
  } catch (error) {
    console.error("Error fetching theme by name:", error);

    return c.json(
      {
        success: false,
        error: "Failed to fetch the theme from the database.",
      },
      500
    );
  }
});

app.delete("/theme/:id", async (c) => {
  const { id } = c.req.param();

  try {
    // Check if theme exists
    const themeExists = await prisma.newThemeDiscover.findUnique({
      where: { id },
    });

    if (!themeExists) {
      return c.json(
        {
          success: false,
          error: "Theme not found.",
        },
        404
      );
    }

    // Delete the theme
    await prisma.newThemeDiscover.delete({
      where: { id },
    });

    return c.json({
      success: true,
      message: "Theme successfully deleted.",
    });
  } catch (error) {
    console.error("Error deleting theme:", error);
    return c.json(
      {
        success: false,
        error: "Failed to delete the theme.",
      },
      500
    );
  }
});

export default app;
