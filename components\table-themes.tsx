// TravelThemesTable.tsx
import React, { useMemo, useState } from 'react';
import {
  Table as ShadCNTable,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from '@/components/ui/table'; // Adjust the import path based on your setup
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  ColumnDef,
  flexRender,
} from '@tanstack/react-table';
import PropTypes from 'prop-types';
import { Button } from './ui/button';   

interface TravelTheme {
  theme: string;
  description: string;
}

interface TravelThemesTableProps {
  data: TravelTheme[];
  rowsPerPage?: number;
  onSelectTheme?: (theme: TravelTheme) => void; // New prop for selection callback
}

const TravelThemesTable: React.FC<TravelThemesTableProps> = ({ data, rowsPerPage = 5, onSelectTheme }) => {
  const [pageSize, setPageSize] = useState<number>(rowsPerPage);
  const [pageIndex, setPageIndex] = useState<number>(0);

  // Define table columns with proper type definitions
  const columns: ColumnDef<TravelTheme, any>[] = useMemo(
    () => [
      {
        header: 'Theme',
        accessorKey: 'theme',
      },
      {
        header: 'Description',
        accessorKey: 'description',
      },
      {
        header: 'Select', // New column for the Select button
        cell: ({ row }) => (
          <Button onClick={() => onSelectTheme?.(row.original)}>
            Select
          </Button>
        ),
      },
    ],
    [onSelectTheme] // Dependency array includes onSelectTheme
  );

  // Initialize the table
  const table = useReactTable({
    data,
    columns,
    initialState: { pagination: { pageSize, pageIndex } },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: false,
  });

  // Handle page size change
  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = Number(e.target.value);
    setPageSize(newSize);
    table.setPageSize(newSize);
  };

  // Handle page navigation
  const handleNextPage = () => {
    table.nextPage();
    setPageIndex(table.getState().pagination.pageIndex);
  };

  const handlePreviousPage = () => {
    table.previousPage();
    setPageIndex(table.getState().pagination.pageIndex);
  };

  return (
    <div className='w-full' >
      <ShadCNTable>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.length > 0 ? (
            table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length}>
                No data available.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </ShadCNTable>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between mt-4">
        <div className='flex gap-2' >
          <Button
            onClick={handlePreviousPage}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            onClick={handleNextPage}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
        <div>
          <span>
            Page{' '}
            <strong>
              {table.getState().pagination.pageIndex + 1} of{' '}
              {table.getPageCount()}
            </strong>
          </span>
          <select
            value={table.getState().pagination.pageSize}
            onChange={handlePageSizeChange}
            className="ml-2 p-1 border rounded"
          >
            {[5, 10, 15, 20].map((size) => (
              <option key={size} value={size}>
                Show {size}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
};

// Define PropTypes for runtime type checking
TravelThemesTable.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      theme: PropTypes.string.isRequired,
      description: PropTypes.string.isRequired,
    })
  ).isRequired,
  rowsPerPage: PropTypes.number,
  onSelectTheme: PropTypes.func, // PropTypes for the new prop
};

export default TravelThemesTable;
