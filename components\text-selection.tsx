"use client"

import { <PERSON><PERSON> } from "./ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import { <PERSON><PERSON>, Highlighter, VolumeIcon as VolumeUp } from 'lucide-react'
import React, { useCallback, useState, useEffect } from 'react'
import { LucideIcon } from "lucide-react";
import { Input } from "./ui/input";
import { useManageSmallDisplay } from "@/hooks/use-manage-small-display";
import { useManageChat } from "@/hooks/use-manage-chat";
import { useManageTranslator } from "@/hooks/use-manage-translator";
import { Pencil, Volume2, BookOpen } from "lucide-react";
import { Message } from "@/hooks/use-manage-chat";

export type Actions =  Array<
    {
      label: string;
      icon: LucideIcon;
      screen?:true,
      llm?:boolean,
      input?:boolean,
      notClose?:true,
      onClick: (
        {
          selectedText, 
          setResult, 
          setIsLoading, 
          input
        }
        :{
          selectedText: string, 
          input?:string, 
          setResult?:(input:string)=>void, 
          setIsLoading?:(input:boolean)=>void 
        }) => void;
    }
>;

interface TextSelectionActionProps {
    children: React.ReactNode;
    className?:string,
    chat?:boolean;
    translate?:boolean;
    smallDiplay?:boolean,
    actions?: Actions;
    props?:React.ComponentProps<'form'>
}


export function TextSelectionActions({ children, className, actions, chat, smallDiplay, translate,props}: TextSelectionActionProps) {
    const [isOpen, setIsOpen] = useState(false)
    const [position, setPosition] = useState({ x: 0, y: 0 })
    const [selectedText, setSelectedText] = useState('')
    const [input, setInput] = useState("")

    const { messages, input : inputChat, setInput: setInputChat, addMessage, setIsLoading : setIsLoadingChat , isLoading : isLoadingChat, addCorrection } = useManageChat()
    const { outputSD, inputSD, setInputSD, isLoading,setOutputSD, setIsLoading } = useManageSmallDisplay();
      const {
        selectedLang,
        setSelectedLang,
        inputText,
        setInputText,
        responseData,
        setResponseData,
        loading,
        setLoading,
        errorMsg,
        setErrorMsg,
        handleTranslate,
      } = useManageTranslator();

      // New Handler: Chat Action
    const handleChatAction = async ({ selectedText, input, setResult, setIsLoading }: { selectedText: string, input?: string, setResult?: (input: string) => void, setIsLoading?: (input: boolean) => void }) => {
      if (!input?.trim()) {
          setResult && setResult("Please enter a message for the chat.");
          return;
      }

      // Add user message to chat
      const userMessage: Message = { role: "user", content:input, attached: selectedText };
      addMessage(userMessage);
      setInputChat('');
      setIsLoadingChat(true);

      try {
          const response = await fetch('/api/chat', {
              method: 'POST',
              headers: {
                  'Content-Type': 'application/json',
              },
              body: JSON.stringify({input:`                   
                ${input}

            target:**${selectedText}**
              `,messages }),
          });

          if (!response.ok) {
              throw new Error('Failed to get response from chat.');
          }

          const data = await response.json();
          addCorrection(data.correctionResponse);
          const assistantMessage: Message = { role: 'assistant', content: data.vocabularyResponse };
          addMessage(assistantMessage);

          setResult && setResult(data.vocabularyResponse);
      } catch (error) {
          console.error('Error:', error);
          setResult && setResult("Failed to get response from chat.");
      } finally {
          setIsLoadingChat(false);
      }
  };

  // New Handler: Translate Action
  const handleTranslateAction = async ({ selectedText, setResult, setIsLoading }: { selectedText: string, input?: string, setResult?: (input: string) => void, setIsLoading?: (input: boolean) => void }) => {
      setInputText(selectedText);
      await handleTranslate();
  };

  // New Handler: Custom Query Action
  const handleCustomQueryAction = async ({ selectedText, input, setResult, setIsLoading }: { selectedText: string, input?: string, setResult?: (input: string) => void, setIsLoading?: (input: boolean) => void }) => {
      if (!input?.trim()) {
          setResult && setResult("Please enter a query.");
          return;
      }

      setIsLoading && setIsLoading(true);
      try {
          const response = await fetch('/api/chat/multifunc', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                  input: 
                  `                   
                    ${input}

                target:**${selectedText}**

                  `,
                  instruction: 'Respond to the user query in a short way:',
              }),
          });

          if (!response.ok) {
              throw new Error('LLM request failed.');
          }

          const data = await response.json();
          setResult && setResult(data.text);


      } catch (error) {

          console.error("Error with Custom Query:", error);
          setResult && setResult("Failed to fetch response.");

      } finally {

          setIsLoading && setIsLoading(false);

      }
  };

  
    const handleTextSelection = useCallback(() => {
      
      const selection = window.getSelection()
      if (selection && selection.toString().trim().length > 0) {
        const range = selection.getRangeAt(0)
        const rect = range.getBoundingClientRect()
        const newPosition = {
          x: rect.left + rect.width / 2,
          y: rect.top - 10,
        }
        setPosition(newPosition)
        setSelectedText(selection.toString())
        setIsOpen(true)
        
      } else {
        setIsOpen(false)
      }
    }, [])
  

    return(
        <div onContextMenu={(e) => e.preventDefault()} onMouseUp={  handleTextSelection} onTouchEnd={handleTextSelection} className={className} >
          <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
              <div
                style={{
                  position: 'fixed',
                  left: `${position.x}px`,
                  top: `${position.y}px`,
                  width: '1px',
                  height: '1px',
                }}
              />
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="center" side="top">
              <div className="grid grid-cols-2 gap-1 p-1">
                {chat && (
                  <Popover>
                  <PopoverTrigger>
                    <Button
                      key={"Chat"}
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        
                      }}
                    >
                      <Volume2 className="h-4 w-4 mr-1" />
                        Chat
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent  className="w-fit">
                    <div className="flex gap-1 ">
                      <Input className="min-w-64" 
                        placeholder="Enter input..."
                        value={input}
                        onChange={(e) => setInput(e.target.value)} />
                      <Button onClick={
                        ()=>{
                          handleChatAction({selectedText, input , setIsLoading:setIsLoading})
                          }
                        } >
                        Submit
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>  
                )}
                {
                  translate && (
                    <Button
                      key={"Translate"}
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        setInputSD(selectedText)
                        handleTranslateAction( {selectedText,setResult: setOutputSD, setIsLoading:setIsLoading} );
                        setIsOpen(false)
                      }}
                    >
                      <BookOpen className="h-4 w-4 mr-1" />
                      "Translate"
                    </Button>
                  )
                }{
                  smallDiplay && (
                    <Popover>
                        <PopoverTrigger>
                          <Button
                            key={"Custom Query"}
                            size="sm"
                            variant="ghost"
                            onClick={() => {
                              
                            }}
                          >
                            <Pencil className="h-4 w-4 mr-1" />
                            "Custom Query"
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent  className="w-fit">
                          <div className="flex gap-1 ">
                            <Input className="min-w-64" 
                              placeholder="Enter input..."
                              value={input}
                              onChange={(e) => setInput(e.target.value)} />
                            <Button onClick={
                              ()=>{
                                handleCustomQueryAction({selectedText, input, setResult: setOutputSD , setIsLoading:setIsLoading})
                                }
                              } >
                              Submit
                            </Button>
                          </div>
                        </PopoverContent>
                      </Popover>  
                  )
                }
                {actions && actions.map((action) => {
                  const Icon = action.icon;

                  if(action.input){
                    return (
                      <Popover>
                        <PopoverTrigger>
                          <Button
                            key={action.label}
                            size="sm"
                            variant="ghost"
                            onClick={() => {
                              
                            }}
                          >
                            <Icon className="h-4 w-4 mr-1" />
                            {action.label}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent  className="w-fit">
                          <div className="flex gap-1 ">
                            <Input className="min-w-64" 
                              placeholder="Enter input..."
                              value={input}
                              onChange={(e) => setInput(e.target.value)} />
                            <Button onClick={
                              ()=>{
                                action.onClick({selectedText, input, setResult: setOutputSD , setIsLoading:setIsLoading})
                                action.notClose ? null : setIsOpen(false)
                                }
                              } >
                              Submit
                            </Button>
                          </div>
                        </PopoverContent>
                      </Popover>      
                )
                  }

                  return (
                    <Button
                      key={action.label}
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        setInputSD(selectedText)
                        action.onClick( {selectedText,setResult: setOutputSD, setIsLoading:setIsLoading} );
                        action.notClose ? null : setIsOpen(false)
                      }}
                    >
                      <Icon className="h-4 w-4 mr-1" />
                      {action.label}
                    </Button>
                  );
                })}
              </div>
            </PopoverContent>
          </Popover>
          {children}
        </div>
      )
}


export default TextSelectionActions