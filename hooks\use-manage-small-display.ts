"use client"; // Ensure this runs on the client side

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { newThemeDiscover } from "@/lib/schemas";
import {z} from "zod"

type ResponseRequest = z.infer<typeof newThemeDiscover>

type Message = {
    role: 'user' | 'assistant'
    content: string
  };

type ChatState = {
  
  inputSD:string,
  outputSD:string,
  setOutputSD: ( outputSD:string ) => void;
  setInputSD: (inputSD:string) => void;
  isLoading: boolean;
  setIsLoading: (isLoading: boolean) => void;

};

export const useManageSmallDisplay= create<ChatState>()(
  devtools((set) => ({

    output:"",
    isLoading: false,
    setOutputSD: (outputSD) => set({ outputSD }),
    setInputSD: (inputSD) => set({inputSD}),
    setIsLoading: (isLoading) => set({ isLoading }),

  }))
);
