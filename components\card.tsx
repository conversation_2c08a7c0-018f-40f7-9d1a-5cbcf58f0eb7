import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    TooltipTrigger,
  } from "@/components/ui/tooltip"
  import { CircleAlert } from "lucide-react";
  import { z } from "zod";
  import { r } from "@/lib/schemas";
  import { Badge } from "@/components/ui/badge"
  import { Button } from "@/components/ui/button";
  import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
  } from "@/components/ui/accordion"
  import { TextSelectionActions } from "./text-selection";
  import { useCallback, useState } from "react";
  import { basicActions, actions } from "@/lib/actions-text-selections";

  //TODO change name "m"

type Props = z.infer<typeof r>

const CardDisplay = ({m}:{m:Props})=>{

        const handleTextToSpeech = (selectedText:string) => {
          const utterance = new SpeechSynthesisUtterance(selectedText)
          window.speechSynthesis.speak(utterance)
        }

    

    return(
      <TextSelectionActions
        chat={true}
        translate={true}
        smallDiplay={true} 
        actions={
          [
            ...basicActions,            
            actions.handleSynonyms,
            actions.handleExamples,
            actions.handleIPA,

          ]
        }
      >
        <div className="gap-2 border bg-slate-50/50 p-2 rounded-lg">
        <div>
          <div className="">
            <div className="font-bold text-slate-800 flex items-center gap-2">{
            m.noun ?
                m.noun :
                 m.adjective ? 
                    m.adjective :
                        m.adverb ?
                            m.adverb :
                                m.verb ?
                                    m.verb
                                    :
                                undefined
            }
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger><CircleAlert size={16} /></TooltipTrigger>
                  <TooltipContent>
                    <p className="text-[14px]  
                    " >{m.meaning}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="text-slate-500" >{m.IPASpelling}</div >
          </div>
          <div className="flex flex-col gap-3" >
            <div className="flex  gap-4" >
              
              <div className="flex flex-wrap gap-1">
                <div className="font-bold text-sm text-slate-800 my-2 mr-2" >synonyms  : </div>
                {m.synonyms.map((s)=>{
                  return (
                    <div className="flex flex-col gap-1 rounded  border p-1">
                        <div className="text-xs font-semibold flex items-center gap-1" >
                          {s.synonym}
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger><CircleAlert size={14} /></TooltipTrigger>
                              <TooltipContent>
                                <p className="text-[14px]  
                                " >{s.meaning}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <span className="text-xs " >{s.IPASpelling}</span>
                      </div>  
                  )
                })}
              </div>
            </div>
            <div className="flex gap-4" >
              <div className="flex flex-wrap gap-1">
                <div className="font-bold  text-sm text-slate-800 my-2 mr-2" >antonyms  :</div>
                {m.antonyms.map((s)=>{
                  return (
                    <div className="flex flex-col gap-1 rounded  border p-1">
                        <div className="text-xs font-semibold flex items-center gap-1" >
                          {s.antonym}
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger><CircleAlert size={14} /></TooltipTrigger>
                              <TooltipContent>
                                <p className="text-[14px]  
                                " >{s.meaning}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <span className="text-xs " >{s.IPASpelling}</span>
                      </div>  
                  )
                })}
              </div>
            </div>
            <div className="flex   gap-4 items-center" >
              
                <div className="flex flex-wrap gap-1">
                  <div className="font-bold text-sm text-slate-800 my-2 mr-2" >collocation  :</div>
                  {m.collocations.map((s)=>{
                    return (
                      <div className="flex flex-col gap-1 rounded  border p-1">
                        <div className="text-xs font-semibold flex items-center gap-1" >
                          {s.collocation}
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger><CircleAlert size={14} /></TooltipTrigger>
                              <TooltipContent>
                                <p className="text-[14px]  
                                " >{s.meaning}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <span className="text-xs " >{s.IPASpelling}</span>
                      </div>  
                    )
                  })}
                </div>
            </div>
            <div>
              <Accordion type="single" collapsible>
                <AccordionItem value="item-1">
                  <AccordionTrigger>derivation</AccordionTrigger>
                  <AccordionContent>
                   { m.derivations && m.derivations.adjectives && ( <div>{m.derivations.adjectives.map((adj)=><Badge>{adj}</Badge>)}</div> ) }
                   { m.derivations && m.derivations.verbs && ( <div>{m.derivations.verbs.map((verb)=><Badge>{verb}</Badge>)}</div> ) }
                   { m.derivations && m.derivations.nouns && ( <div>{m.derivations.nouns.map((verb)=><Badge>{verb}</Badge>)}</div> ) }
                  </AccordionContent>
                </AccordionItem>
            </Accordion>
            </div>
          </div>
        </div>
      </div>
      </TextSelectionActions>
    )
}

 
export default CardDisplay