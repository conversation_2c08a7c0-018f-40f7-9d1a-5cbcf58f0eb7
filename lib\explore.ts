import { AIMessage, BaseMessage, SystemMessage} from "@langchain/core/messages";
import { tool,DynamicTool } from "@langchain/core/tools";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { StateGraph,START,END,Annotation  } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";
import { MemorySaver, Send } from "@langchain/langgraph";
import {z} from "zod"
import { sp_IPA, sp_WD } from "./prompt";
import { RunnableLike,RunnableToolLike } from "@langchain/core/runnables";
import { nounsGen,nouns, verbsGen, verbs, adjectivesGen, adjectives, adverbsGen, adverbs, newThemeDiscover, mnemonic as mnemonicSchema } from "./schemas";
import {PromptTemplate} from "@langchain/core/prompts"
import { Anonymous_Pro } from "next/font/google";

//TODO expand this to be structured with classes

type NestedArray<T> = T | NestedArray<T>[]

const AgentState = Annotation.Root({
    messages:Annotation<BaseMessage[]>({
        reducer:(x,y) => x.concat(y),
        default:()=>[new SystemMessage(sp_IPA)]
    })
})

export class Agent {
    model: ChatOpenAI ;
    State: typeof AgentState;
    tools: DynamicTool[]


    constructor({model,State,tools}:{model: ChatOpenAI, State:typeof AgentState,tools:DynamicTool[] }){
        this.model = model;
        this.State = State;
        this.tools = tools
    }


    create(){

        const toolNode = new ToolNode(this.tools)

        const shouldContinue = (state: typeof AgentState.State)=>{
            const lastMessage = state.messages[state.messages.length - 1]
            
            if(lastMessage && !(lastMessage as AIMessage).tool_calls?.length ){
                    return END
            }
        
            return "action"
        }

        const agentNode = async (state:typeof AgentState.State)=>{
            const messages = state.messages
            console.log(messages)
        
            try{
                const response = await this.model.invoke(messages)
                return { messages: [response] }
            }catch(err){
                console.log(err)
                return { messages: [new AIMessage("error")] }
            }
        }

        const workflow = new StateGraph(this.State)
        .addNode("agent",agentNode)
        .addNode("action",toolNode)
        .addEdge(START,"agent")
        .addEdge("action","agent")
        .addConditionalEdges("agent",shouldContinue)

        const agent = workflow.compile({
            checkpointer: memory
        })

        return agent
    }   
}

const memory =new MemorySaver()

const openai = new ChatOpenAI({
    model:"gpt-4o"
})



const AgentWordDiscover = Annotation.Root({
    messages:Annotation<BaseMessage[]>({
        reducer:(x,y) => x.concat(y),
        default:()=>[new SystemMessage(sp_WD)]
    })
})

const searchtool = tool((input) => {
    return "search result"
},{

    name:"searchtool",
    description:"search tool",
    schema: z.string().describe("search to be made")

    }
) 

//done 
const toolNode = new ToolNode([searchtool])

const shouldContinue = (state: typeof AgentState.State)=>{
    const lastMessage = state.messages[state.messages.length - 1]
    
    if(lastMessage && !(lastMessage as AIMessage).tool_calls?.length ){
            return END
    }

    return "action"
}

const agentNode = async (state:typeof AgentState.State)=>{
    const messages = state.messages
    console.log(messages)

    try{
        const response = await openai.invoke(messages)
        return { messages: [response] }
    }catch(err){
        console.log(err)
        return { messages: [new AIMessage("error")] }
    }
}

const workflow = new StateGraph(AgentState)
    .addNode("agent",agentNode)
    .addNode("action",toolNode)
    .addEdge(START,"agent")
    .addEdge("action","agent")
    .addConditionalEdges("agent",shouldContinue)
    


export const agent = workflow.compile({
    checkpointer: memory
})

const workflowDiscover = new StateGraph(AgentWordDiscover)
    .addNode("agent",agentNode)
    .addNode("action",toolNode)
    .addEdge(START,"agent")
    .addEdge("action","agent")
    .addConditionalEdges("agent",shouldContinue)
    


export const agentDiscover = workflowDiscover.compile({
    checkpointer: memory
})

//--------------------------------------------------------------------------//
//TODO create class

//---Nouns

const GraphState = Annotation.Root({

    theme:Annotation< string >,
    nouns:Annotation< z.infer<typeof nouns> | string >,
    verbs:Annotation< z.infer<typeof verbs> | string >,
    adverbs:Annotation< z.infer<typeof adverbs> | string >,
    adjectives:Annotation< z.infer<typeof adjectives> | string >,
    subCategory:Annotation< string[] | string >,
    deepness:Annotation<number>({
        reducer:(x)=>x++,
        default:()=>0
    }),
    subCExpanded:Annotation<NestedArray<z.infer<typeof newThemeDiscover>>[]>({
        reducer:(x,y)=> x.concat(y)
    })

})

const mnemonicModel =  new ChatOpenAI({
    model:"gpt-4o"
}).withStructuredOutput(z.object({
    hightFrequency:mnemonicSchema,
    middleFrequency:mnemonicSchema
}))

const promptMnemonic = PromptTemplate.fromTemplate(`You are an AI memory coach specializing in maximizing word retention using mnemonic techniques. Your goal is to help users memorize a given list of words by applying the **Key Word Method** and **Association and Visualization techniques**
    respond following the provided json structure.
    input:{input}
`)
const mnemonicChain = promptMnemonic.pipe(mnemonicModel)

//---start node

const startNode = (state:typeof GraphState.State)=>{

    return state
}

//---subcategory

const themeExtender = new ChatOpenAI({
    model:"gpt-4o"
}).withStructuredOutput(z.object({subCategory:z.array(z.string()).max(4).describe("The subcategory of the theme being described.")}))

const extenderAgentNode = async (state:typeof GraphState.State)=>{

    console.log(`--EXTENDER NODE--`)

    const  prompt =  PromptTemplate.fromTemplate(`You are an AI language assistant designed to help users expand their vocabulary based on specific themes. When provided with a theme, your task is to identify up to four relevant subcategories related to that theme. For each subcategory, generate a list of associated words that aid in vocabulary expansion.
        input:{input}
        `)
     const chain = prompt.pipe(themeExtender)
     let success = false
     let attempts = 0
 
     while (!success && attempts < 5) {
 
         try{

             const response = await chain.invoke({input:state.theme})
             success = true
             console.log(`### subCategory : ${JSON.stringify(response, null, 2)} `)
             console.log(`--END EXTENDER deepness:${state.deepness} theme: ${JSON.stringify(state.theme)}`)
            return {subCategory:[response.subCategory]}
            
         }catch(err){
 
             console.log(err)
             return {subCategory:`error generating nouns for the theme ${state.theme}`}
 
         }
 
     }
     
 }

 //---shouldGoDeep

 const shouldDeep = (state: typeof GraphState.State)=>{

    console.log(`--SHOULD DEEP--`)

    if(state.deepness > 2 || typeof state.subCategory === "string"){
        console.log("--END--")
        return "end"
    }else{
        console.log(state.subCategory)
        return state.subCategory.map((category:string)=> { console.log(JSON.stringify(category,null, 2)) 
            return new Send("nextLevel",{theme:category,deepness:state.deepness})})
    }
    
 }

 //---NextLevel


/*  const nextLevel = async ({theme,deepness}:{theme:string,deepness:number}): Promise<Partial<typeof GraphState.State>>=>{

    console.log(`### deepness : ${deepness}  ### theme: ${JSON.stringify(theme, null, 2)}`)
    const response = await secondAgent.invoke({theme:theme, deepness})
    return {subCExpanded:[response], deepness : 1 }
 } */

//---nouns

const theDetaillerNouns = new ChatOpenAI({
    model:"gpt-4o"
}).withStructuredOutput(nounsGen)


const nounsAgentNode = async (state:typeof GraphState.State)=>{

    console.log(`---NOUNS NODE---`)

   const  prompt =  PromptTemplate.fromTemplate(`You are an AI language assistant designed to help users expand their vocabulary by providing nouns associated with specific themes. When given a theme, your task is to generate a comprehensive list of relevant and diverse nouns related to that theme.
    here is the theme:{input}
    `)
    const chain = prompt.pipe(theDetaillerNouns)
    let success = false
    let attempts = 0

    while (!success && attempts < 5) {

        try{

            const response = await chain.invoke({input:state.theme})
            success = true

            console.log(`### nouns : ${JSON.stringify(response, null, 2)}  ### theme: ${JSON.stringify(state.theme, null, 2)}`)

            const mnemonic = await mnemonicChain.invoke({input:JSON.stringify(response, null, 2)})
            const finalRes = {
                usage:{
                    hightFrequency:{
                        ...response.usage.hightFrequency,
                        mnemonic:mnemonic.hightFrequency
                    },
                    middleFrequency:{
                        ...response.usage.middleFrequency,
                        mnemonic:mnemonic.middleFrequency
                    }
                }
            }

            console.log(`### mnemonic : ${JSON.stringify(mnemonic, null, 2)}  ### theme: ${JSON.stringify(state.theme, null, 2)}`)
            console.log(`--END NOUNS deepness:${state.deepness} ### theme: ${JSON.stringify(state.theme, null, 2)}`)
            return {nouns:[finalRes]} 

        }catch(err){

            console.log(err)
            return {nouns:`error generating nouns for the theme ${state.theme}`}

        }

    }
    
}

//---Verbs

const theDetaillerVerbs = new ChatOpenAI({
    model:"gpt-4o"
}).withStructuredOutput(verbsGen)

const verbsAgentNode = async (state:typeof GraphState.State)=>{

    console.log(`---VERBS NODE---`)

    const  prompt =  PromptTemplate.fromTemplate(`You are an AI language assistant designed to help users expand their vocabulary by providing verbs associated with specific themes. When given a theme, your task is to generate a comprehensive list of relevant and diverse verbs (with or without prepositions) related to that theme.
    here is the theme:{input}
    `)
     const chain = prompt.pipe(theDetaillerVerbs)
     let success = false 
     let attempts = 0
 
     while (!success && attempts < 5) {
 
         try{
 
             const response = await chain.invoke({input:state.theme})
             success = true 

             console.log(`### verbs : ${JSON.stringify(response, null, 2)}  ### theme: ${state.theme}`)
             const mnemonic = await mnemonicChain.invoke({input:JSON.stringify(response, null, 2)})
             const finalRes = {
                 usage:{
                     hightFrequency:{
                         ...response.usage.hightFrequency,
                         mnemonic:mnemonic.hightFrequency
                     },
                     middleFrequency:{
                         ...response.usage.middleFrequency,
                         mnemonic:mnemonic.middleFrequency
                     }
                 }
             }

             console.log(`### mnemonic : ${JSON.stringify(mnemonic)}  ### theme: ${JSON.stringify(state.theme)}`)
             console.log(`--END VERBS deepness:${state.deepness} ### theme: ${JSON.stringify(state.theme)}`)
             return {verbs:[finalRes]} 
 
         }catch(err){
 
             console.log(err)
             return {verbs:`error generating verbs for the theme ${state.theme}`}
 
         }
 
     }  
 }

//---adjectives

const theDetaillerAdjs = new ChatOpenAI({
    model:"gpt-4o"
}).withStructuredOutput(adjectivesGen)

const adjectivesAgentNode = async (state:typeof GraphState.State)=>{

    console.log(`---ADJECTIVES NODE---`)

    const  prompt =  PromptTemplate.fromTemplate(`You are an AI language assistant designed to help users expand their vocabulary by providing adjectives associated with specific themes. When given a theme, your task is to generate a comprehensive list of relevant and diverse adjectives related to that theme.
    here is the theme:{input}
    `)
     const chain = prompt.pipe(theDetaillerAdjs)
     let success = false 
     let attempts = 0
 
     while (!success && attempts < 5) {
 
         try{
 
             const response = await chain.invoke({input:state.theme})
             success = true

             console.log(`### adjectives : ${JSON.stringify(response, null, 2)}  ### theme: ${JSON.stringify(state.theme)}`)

             const mnemonic = await mnemonicChain.invoke({input:JSON.stringify(response, null, 2)})
             const finalRes = {
                 usage:{
                     hightFrequency:{
                         ...response.usage.hightFrequency,
                         mnemonic:mnemonic.hightFrequency
                     },
                     middleFrequency:{
                         ...response.usage.middleFrequency,
                         mnemonic:mnemonic.middleFrequency
                     }
                 }
             }

            console.log(`### mnemonic : ${JSON.stringify(mnemonic)}  ### theme: ${state.theme}`)
            console.log(`--END ADJECTIVES deepness:${state.deepness} ### theme: ${JSON.stringify(state.theme)}`)
             return {adjectives:[finalRes]} 
 
         }catch(err){
 
             console.log(err)
             return {adjectives:`error generating adjectives for the theme ${state.theme}`}
 
         }
 
     }
     
 }

//---adverbs

const theDetaillerAdvs = new ChatOpenAI({
    model:"gpt-4o"
}).withStructuredOutput(adverbsGen)


const adverbsAgentNode = async (state:typeof GraphState.State)=>{

    console.log(`---ADVERBS NODE---`)

    const  prompt =  PromptTemplate.fromTemplate(`You are an AI language assistant designed to help users expand their vocabulary by providing adverbs associated with specific themes. When given a theme, your task is to generate a comprehensive list of relevant and diverse adverbs related to that theme.
    here is the theme:{input}
    `)
     const chain = prompt.pipe(theDetaillerAdvs)
     let success = false 
     let attempts = 0
 
     while (!success && attempts < 5) {
 
         try{
 
             const response = await chain.invoke({input:state.theme})
             success = true

             console.log(`### adverbs : ${JSON.stringify(response, null, 2)}  ### theme: ${state.theme}`)

             const mnemonic = await mnemonicChain.invoke({input:JSON.stringify(response, null, 2)})
             const finalRes = {
                 usage:{
                     hightFrequency:{
                         ...response.usage.hightFrequency,
                         mnemonic:mnemonic.hightFrequency
                     },
                     middleFrequency:{
                         ...response.usage.middleFrequency,
                         mnemonic:mnemonic.middleFrequency
                     }
                 }
             }

             console.log(`### mnemonic : ${JSON.stringify(mnemonic)}  ### theme: ${JSON.stringify(state.theme)}`)
             console.log(`--END ADVERBS deepness:${state.deepness} ### theme: ${JSON.stringify(state.theme)}`)

             return {adverbs:[finalRes]} 
 
         }catch(err){
 
             console.log(err)
             return {adverbs:`error generating adverbs for the ### theme: ${JSON.stringify(state.theme)}`}
 
         }
 
     }
     
 }

 const endGraph = (state:typeof GraphState.State)=>{
    console.log(`--- END ---`)
    return state
 }

 const secondGraph = new StateGraph(GraphState)
    .addNode("nounsAgentNode",nounsAgentNode)
    .addNode("verbsAgentNode",verbsAgentNode)
    .addNode("adjectivesAgentNode",adjectivesAgentNode)
    .addNode("adverbsAgentNode",adverbsAgentNode)
    .addNode("extenderAgentNode",extenderAgentNode)
    /* .addNode("nextLevel",nextLevel) */
    .addNode("startNode",startNode)
    .addNode("end",endGraph)
    .addEdge(START,"startNode")
    .addEdge("startNode","extenderAgentNode")
    .addEdge("extenderAgentNode","end")
    .addEdge("startNode","nounsAgentNode")
    .addEdge("nounsAgentNode","end")
    .addEdge("startNode","adjectivesAgentNode")
    .addEdge("adjectivesAgentNode","end")
    .addEdge("startNode","adverbsAgentNode")
    .addEdge("adverbsAgentNode","end")
    .addEdge("extenderAgentNode","end")
    .addConditionalEdges("extenderAgentNode",shouldDeep)
    .addEdge("end",END)
    
export  const secondAgent = secondGraph.compile()

//---next level