// Essay.tsx
"use client";

import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import ReactQuill, { Quill } from "react-quill";
import 'react-quill/dist/quill.snow.css';
import MarkdownTypewriter from "./markdowntyper";
import { schemaThemes } from "@/lib/schemas";
import { z } from "zod";
import TravelThemesTable from "./table-themes";


interface Props{
    time?:number,
    topic?:string,
    title:string
}

type ThemeSchema = z.infer<typeof schemaThemes>
type Themes = ThemeSchema['themes'];

interface TravelTheme {
  theme: string;
  description: string;
}

export default function Essay ({time, topic:topc, title}:Props) {

  const Font = Quill.import('formats/font');
  
  Font.whitelist = ['sofia', 'slabo', 'roboto', 'inconsolata', 'ubuntu'];
  Quill.register(Font, true);


  const modules = {
    toolbar: [
      // Dropdowns for font family and size
      [{ 'font': Font.whitelist }, { 'size': ['small', false, 'large', 'huge'] }], // Use 'false' for default size

      // Header dropdown
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],

      // Text formatting buttons
      ['bold', 'italic', 'underline', 'strike'],

      // Color pickers
      [{ 'color': [] }, { 'background': [] }],

      // Script (subscript/superscript) and blockquote
      [{ 'script': 'sub' }, { 'script': 'super' }, 'blockquote'],

      // List and indent buttons
      [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'indent': '-1' }, { 'indent': '+1' }],

      // Alignment dropdown
      [{ 'align': [] }],

      // Direction (LTR/RTL)
      [{ 'direction': 'rtl' }],

      // Link, image, video, and formula (math) buttons
      ['link', 'image', 'video', 'formula'],

      // Code block
      ['code-block'],

      // Clean formatting button
      ['clean']
    ],
  };
    //TODO random essay



  const [essay,setEssay]=useState("")
  const [topic, setTopic] = useState("");
  const [essayIdeas, setEssayIdeas] = useState<Themes>([]);
  const [loading, setLoading] = useState(false);
  const [correction,setCorrection] = useState("")
  const [selectedTheme, setSelectedTheme] = useState<TravelTheme | null>(null); // New state for selected theme
  const [showTable, setShowTable] = useState(true); // New state to control table visibility

  console.log(essayIdeas)
  
  useEffect(()=>{
    if(topc) setTopic(topc)
  },[topc]) // Added topc as a dependency



  const handleGetSomeIdeas = async () => {
    setLoading(true);
    setEssayIdeas([]);
    try {
      const res = await fetch("/api/essay/theme-essay", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ topic , simulation:false }),
      });
      if (!res.ok) {
        throw new Error("Failed to write essay.");
      }
      const {themes} = await res.json();
      setEssayIdeas(themes.themes);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleForCorrection = async () =>{
    setLoading(true);
    try {
        const res = await fetch("/api/essay/correction-essay", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ topic , simulation:false }),
        });
        if (!res.ok) {
          throw new Error("Failed to get correction");
        }

        const data = await res.json();
        setCorrection(data);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false);
      }
  }

  // Handler for selecting a theme from the table
  const handleSelectTheme = (theme: TravelTheme) => {
    setSelectedTheme(theme);
    setShowTable(false); // Hide the table upon selection
    setTopic(theme.theme); // Optionally, set the topic to the selected theme
  };

  // Handler to show the table again
  const handleShowTable = () => {
    setShowTable(true);
    setSelectedTheme(null); // Reset the selected theme
  };

  return (
    <main className="p-6 w-full">
      <div className="w-full bg-white p-4 rounded">
        <h1 className="text-2xl font-bold mb-4">{title}</h1>

        {/* Input for theme (only if not provided via props and no theme is selected) */}
        {!topc && !selectedTheme && (
          <input
            className="border p-2 mb-4 w-full rounded"
            value={topic}
            onChange={(e) => setTopic(e.target.value)}
            placeholder="Type a theme or topic"
          />
        )}

        {/* Button to get essay ideas (only if no theme is selected) */}
        {!selectedTheme && (
          <Button onClick={handleGetSomeIdeas} disabled={!topic || loading}>
            {loading ? "Loading..." : "Get Some Ideas"}
          </Button>
        )}

        {/* Button to show the table again when it's hidden */}
        {!showTable && (
          <Button onClick={handleShowTable} className="mt-4">
            Show Ideas
          </Button>
        )}

        {/* Display the Table if showTable is true and there are essay ideas */}
        {showTable && essayIdeas.length > 0 && (
          <div className="my-4">
            <TravelThemesTable data={essayIdeas} onSelectTheme={handleSelectTheme} />
          </div>
        )}

        {/* Optionally display details of the selected theme */}
        {selectedTheme && (
          <div className="my-4 p-4 bg-gray-100 rounded">
            <h2 className="text-xl font-semibold">Selected Theme:</h2>
            <p><strong>Theme:</strong> {selectedTheme.theme}</p>
            <p><strong>Description:</strong> {selectedTheme.description}</p>
          </div>
        )}

        {/* ReactQuill Editor for writing the essay */}
        <ReactQuill value={essay} modules={modules} onChange={setEssay} />

        {/* Submit Button for Correction */}
        <div>
          <Button className="mt-4" onClick={handleForCorrection} disabled={!essay || loading}>
            Submit
          </Button>
        </div>

        {/* Display Correction */}
        <div>
          {correction && <MarkdownTypewriter content={correction} />}
        </div>
      </div>
    </main>
  );
}
