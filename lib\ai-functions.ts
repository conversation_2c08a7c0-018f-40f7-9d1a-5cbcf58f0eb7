import { AIMessage, BaseMessage, SystemMessage} from "@langchain/core/messages";
import { tool,DynamicTool } from "@langchain/core/tools";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { StateGraph,START,END,Annotation  } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";
import { MemorySaver } from "@langchain/langgraph";
import {z} from "zod"
import { sp_IPA, sp_WD } from "./prompt";
import { RunnableLike,RunnableToolLike, Runnable } from "@langchain/core/runnables";

//TODO expand this to be structured with classes

const AgentState = Annotation.Root({
    messages:Annotation<BaseMessage[]>({
        reducer:(x,y) => x.concat(y),
        default:()=>[new SystemMessage(sp_IPA)]
    })
})

export class Agent {
    model: Runnable ;
    State: typeof AgentState;
    tools: DynamicTool[]


    constructor({model,State,tools}:{model:Runnable, State:typeof AgentState,tools:DynamicTool[] }){
        this.model = model;
        this.State = State;
        this.tools = tools
    }


    create(){

        const memory =new MemorySaver()
        const toolNode = new ToolNode(this.tools)

        const shouldContinue = (state: typeof AgentState.State)=>{
            const lastMessage = state.messages[state.messages.length - 1]
            
            if(lastMessage && !(lastMessage as AIMessage).tool_calls?.length ){
                    return END
            }
        
            return "action"
        }

        const agentNode = async (state:typeof AgentState.State)=>{
            const messages = state.messages
            console.log(messages)
        
            try{
                const response = await this.model.invoke(messages)
                return { messages: [response] }
            }catch(err){
                console.log(err)
                return { messages: [new AIMessage("error")] }
            }
        }

        const workflow = new StateGraph(this.State)
        .addNode("agent",agentNode)
        .addNode("action",toolNode)
        .addEdge(START,"agent")
        .addEdge("action","agent")
        .addConditionalEdges("agent",shouldContinue)

        const agent = workflow.compile({
            checkpointer: memory
        })

        return agent
    }   
}
