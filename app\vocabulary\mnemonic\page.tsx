"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Volume2, RefreshCw, Info, Check, Bookmark, X, ChevronDown, ChevronRight } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { TextSelectionActions } from "@/components/text-selection";
import { basicActions, actions } from "@/lib/actions-text-selections";
import { useToast } from "@/hooks/use-toast";

type Substitution = {
  word: string;
  ipa: string;
  french: string;
  category: string;
  notes?: string;
};

type MnemonicAnalysis = {
  mnemonic_analysis: {
    input_word: string;
    input_ipa: string;
    input_french: string;
    substitutions: {
      substitution: Substitution[];
    };
  };
};

const MNEMONIC_CATEGORIES = [
  "Homophonic Replacement",
  "Blending & Resegmentation",
  "Insertion or Deletion of a Segment",
  "Metathesis & Transposition",
  "Semantic Elaboration",
  "Phonotactic Stretching",
  "Alliteration & Rhythmic Echo"
];

const CATEGORY_DESCRIPTIONS = {
  "Homophonic Replacement": "Words or morphemes swapped with similar-sounding alternatives",
  "Blending & Resegmentation": "Regrouping syllables to create connections between familiar words",
  "Insertion or Deletion of a Segment": "Adding or removing sounds to create better matches",
  "Metathesis & Transposition": "Swapping positions of syllables or sounds",
  "Semantic Elaboration": "Using vivid mental images with similar-sounding words",
  "Phonotactic Stretching": "Extending sounds across additional syllables",
  "Alliteration & Rhythmic Echo": "Matching initial consonants or rhythmic patterns"
};

export default function MnemonicPage() {
  const { toast } = useToast();
  const [word, setWord] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<string>("all");
  const [categoryLoading, setCategoryLoading] = useState<string | null>(null);
  const [advancedMode, setAdvancedMode] = useState(false);
  const [uniqueCategories, setUniqueCategories] = useState<string[]>([]);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [selectedLanguages, setSelectedLanguages] = useState<string[]>(["english"]);
  const [languageResults, setLanguageResults] = useState<Record<string, MnemonicAnalysis | null>>({
    english: null
  });
  const [activeLanguage, setActiveLanguage] = useState<string>("english");
  const [languageLoading, setLanguageLoading] = useState<string[]>([]);
  const [generationCount, setGenerationCount] = useState<number>(5);
  const [pinnedMnemonics, setPinnedMnemonics] = useState<Array<{
    id: string;
    word: string;
    substitution: Substitution;
    language: string;
  }>>([]);

  // Generate a unique ID for each pinned mnemonic
  const generateId = () => {
    return Math.random().toString(36).substring(2, 9);
  };

  // Function to pin a mnemonic
  const pinMnemonic = (substitution: Substitution, language: string) => {
    setPinnedMnemonics(prev => [
      ...prev, 
      {
        id: generateId(),
        word: word,
        substitution: substitution,
        language: language
      }
    ]);
    toast({
      title: "Mnemonic pinned",
      description: `"${substitution.word}" has been pinned to your collection.`,
      duration: 3000,
    });
  };

  // Function to unpin a mnemonic
  const unpinMnemonic = (id: string) => {
    setPinnedMnemonics(prev => prev.filter(item => item.id !== id));
    toast({
      title: "Mnemonic unpinned",
      description: "The mnemonic has been removed from your collection.",
      duration: 3000,
    });
  };

  // Add a function to clear all pins
  const clearAllPins = () => {
    setPinnedMnemonics([]);
    toast({
      title: "All pins cleared",
      description: "Your pinned mnemonics collection has been cleared.",
      duration: 3000,
    });
  };

  // Add a function to group pinned mnemonics by word
  const groupPinnedByWord = () => {
    const groups: Record<string, typeof pinnedMnemonics> = {};
    
    pinnedMnemonics.forEach(pin => {
      if (!groups[pin.word]) {
        groups[pin.word] = [];
      }
      groups[pin.word].push(pin);
    });
    
    return groups;
  };

  // Add useEffect for localStorage persistence
  useEffect(() => {
    const savedPins = localStorage.getItem('pinnedMnemonics');
    if (savedPins) {
      try {
        setPinnedMnemonics(JSON.parse(savedPins));
      } catch (e) {
        console.error('Failed to parse pinned mnemonics from localStorage', e);
      }
    }
  }, []);

  // Save pinned mnemonics to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('pinnedMnemonics', JSON.stringify(pinnedMnemonics));
  }, [pinnedMnemonics]);

  // Add a component for displaying pinned mnemonics
  const PinnedMnemonicsPanel = () => {
    const [isOpen, setIsOpen] = useState(true);
    const [groupByWord, setGroupByWord] = useState(true);
    
    if (pinnedMnemonics.length === 0) return null;
    
    const groupedPins = groupByWord ? groupPinnedByWord() : null;
    
    return (
      <Card className="w-full h-full sticky top-6">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>Pinned Mnemonics ({pinnedMnemonics.length})</CardTitle>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setGroupByWord(!groupByWord)}
                className="h-8 text-xs"
              >
                {groupByWord ? "Flat" : "Group"}
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={clearAllPins}
                className="h-8 text-xs"
              >
                Clear
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setIsOpen(!isOpen)}
                className="h-8 w-8 p-0"
              >
                {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </CardHeader>
        
        {isOpen && (
          <CardContent className="max-h-[calc(100vh-200px)] overflow-y-auto">
            {groupByWord ? (
              <div className="flex flex-col gap-4">
                {Object.entries(groupedPins || {}).map(([word, pins]) => (
                  <div key={word} className="border rounded-lg p-3">
                    <h3 className="text-md font-semibold mb-2">{word}</h3>
                    <div className="flex flex-col space-y-3">
                      {pins.map((pinned) => (
                        <div 
                          key={pinned.id} 
                          className="flex flex-col p-2 border rounded-lg bg-slate-50 hover:bg-slate-100 transition-colors"
                        >
                          <div className="flex items-center justify-between mb-1">
                            <div className="flex items-center gap-1">
                              <Badge variant="outline" className="capitalize text-xs">{pinned.language}</Badge>
                              <Badge variant="secondary" className="text-xs">{pinned.substitution.category}</Badge>
                            </div>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              onClick={() => unpinMnemonic(pinned.id)}
                              className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                          
                          <div className="flex items-center gap-1 mb-1">
                            <h4 className="text-md font-semibold">{pinned.substitution.word}</h4>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              onClick={() => speakText(pinned.substitution.word)}
                              className="h-5 w-5 p-0"
                            >
                              <Volume2 className="h-3 w-3" />
                            </Button>
                          </div>
                          
                          <div className="text-xs text-gray-600">
                            <p className="font-mono">{pinned.substitution.ipa}</p>
                            {pinned.substitution.notes && (
                              <p className="text-xs italic mt-1">{pinned.substitution.notes}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col space-y-3">
                {pinnedMnemonics.map((pinned) => (
                  <div 
                    key={pinned.id} 
                    className="flex flex-col p-2 border rounded-lg bg-slate-50 hover:bg-slate-100 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-1 flex-wrap">
                        <Badge className="text-xs">{pinned.word}</Badge>
                        <Badge variant="outline" className="capitalize text-xs">{pinned.language}</Badge>
                        <Badge variant="secondary" className="text-xs">{pinned.substitution.category}</Badge>
                      </div>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => unpinMnemonic(pinned.id)}
                        className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                    
                    <div className="flex items-center gap-1 mb-1">
                      <h4 className="text-md font-semibold">{pinned.substitution.word}</h4>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => speakText(pinned.substitution.word)}
                        className="h-5 w-5 p-0"
                      >
                        <Volume2 className="h-3 w-3" />
                      </Button>
                    </div>
                    
                    <div className="text-xs text-gray-600">
                      <p className="font-mono">{pinned.substitution.ipa}</p>
                      {pinned.substitution.notes && (
                        <p className="text-xs italic mt-1">{pinned.substitution.notes}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        )}
      </Card>
    );
  };

  // Add pin button to each mnemonic card
  const renderSubstitutionCard = (sub: any, index: number) => (
    <div key={index} className="border rounded-lg p-4 bg-slate-50 hover:bg-slate-100 transition-colors">
      <div className="flex flex-wrap items-center justify-between mb-2 gap-2">
        <div className="flex items-center gap-2">
          <h3 className="text-xl font-semibold">{sub.word}</h3>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => speakText(sub.word)}
            className="h-8 w-8 p-0"
          >
            <Volume2 className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary">{sub.category}</Badge>
          
          {/* Pin button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => pinMnemonic(sub, activeLanguage)}
                  className="h-8 w-8 p-0"
                >
                  <Bookmark className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Pin this mnemonic to your collection</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => generateMoreForCategory(sub.category, activeLanguage)}
                  disabled={categoryLoading === sub.category || languageLoading.includes(activeLanguage)}
                  className="h-8 flex items-center gap-1"
                >
                  {categoryLoading === sub.category ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <RefreshCw className="h-3 w-3" />
                  )}
                  More like this
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Generate more mnemonics using the {sub.category} technique</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
      
      {/* Rest of the substitution display */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-500">IPA Pronunciation:</p>
          <p className="font-mono">{sub.ipa}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">French Translation:</p>
          <div className="flex items-center gap-2">
            <p>{sub.french}</p>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => speakFrench(sub.french)}
              className="h-6 w-6 p-0"
            >
              <Volume2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>
      
      {sub.notes && (
        <div className="bg-white p-3 rounded border border-slate-200">
          <p className="text-sm text-gray-500 mb-1">Notes:</p>
          <p className="text-gray-700">{sub.notes}</p>
        </div>
      )}
    </div>
  );

  // Replace the single language state
  // const [substitutionLanguage, setSubstitutionLanguage] = useState<string>("english");

  // Update unique categories whenever languageResults or activeLanguage changes
  useEffect(() => {
    const currentResult = languageResults[activeLanguage];
    if (currentResult) {
      const categories = currentResult.mnemonic_analysis.substitutions.substitution
        .map(sub => sub.category)
        .filter((value, index, self) => self.indexOf(value) === index);
      setUniqueCategories(["all", ...categories]);
    }
  }, [languageResults, activeLanguage]);

  // Clear success message after 3 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!word.trim()) return;

    setLoading(true);
    setError("");
    setLanguageResults({});
    setSuccessMessage(null);
    setLanguageLoading([...selectedLanguages]);

    try {
      // Create an array of promises for each language
      const languagePromises = selectedLanguages.map(async (language) => {
        const response = await fetch("/api/mnemonic", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ 
            word: word.trim(),
            specificCategories: advancedMode && selectedCategories.length > 0 ? selectedCategories : undefined,
            language: language,
            count: generationCount
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to generate mnemonic analysis for ${language}`);
        }

        return { language, data: await response.json() };
      });

      // Process all language results
      const results = await Promise.allSettled(languagePromises);
      
      // Create a new results object
      const newResults: Record<string, MnemonicAnalysis | null> = {};
      
      // Process each result
      results.forEach((result, index) => {
        const language = selectedLanguages[index];
        if (result.status === 'fulfilled') {
          newResults[language] = result.value.data;
        } else {
          setError(prev => prev ? `${prev}, ${language}` : `Failed for: ${language}`);
          newResults[language] = null;
        }
      });
      
      setLanguageResults(newResults);
      setActiveLanguage(selectedLanguages[0]);
      setSuccessMessage("Mnemonics generated successfully!");
    } catch (err: any) {
      setError(err.message || "An error occurred");
    } finally {
      setLoading(false);
      setLanguageLoading([]);
    }
  };

  const handleCategorySelect = (category: string) => {
    setSelectedCategories(prev => {
      if (prev.includes(category)) {
        return prev.filter(c => c !== category);
      } else {
        return [...prev, category];
      }
    });
  };

  const generateMoreForCategory = async (category: string, language: string) => {
    if (!word.trim() || !languageResults[language]) return;
    
    setCategoryLoading(category);
    setLanguageLoading(prev => [...prev, language]);
    setSuccessMessage(null);
    
    try {
      const response = await fetch("/api/mnemonic", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          word: word.trim(),
          category: category,
          language: language,
          count: generationCount
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate more ${category} mnemonics for ${language}`);
      }

      const data = await response.json();
      
      // Add new results to existing ones instead of replacing
      setLanguageResults(prevResults => {
        const prevResult = prevResults[language];
        if (!prevResult) return { ...prevResults, [language]: data };
        
        // Get the new substitutions
        const newSubstitutions = data.mnemonic_analysis.substitutions.substitution;
        
        // Create a new result object with combined substitutions
        return {
          ...prevResults,
          [language]: {
            mnemonic_analysis: {
              ...prevResult.mnemonic_analysis,
              substitutions: {
                substitution: [
                  ...newSubstitutions,
                  ...prevResult.mnemonic_analysis.substitutions.substitution
                ]
              }
            }
          }
        };
      });
      
      setActiveTab(category);
      setSuccessMessage(`Added more ${category} mnemonics for ${language}!`);
    } catch (err: any) {
      setError(err.message || "An error occurred");
    } finally {
      setCategoryLoading(null);
      setLanguageLoading(prev => prev.filter(lang => lang !== language));
    }
  };

  const speakText = (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'en-US';
      window.speechSynthesis.speak(utterance);
    }
  };

  const speakFrench = (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'fr-FR';
      window.speechSynthesis.speak(utterance);
    }
  };

  // Add language selection UI
  const languageOptions = [
    { value: "english", label: "English" },
    { value: "french", label: "French" },
    { value: "german", label: "German" },
    { value: "spanish", label: "Spanish" },
    { value: "italian", label: "Italian" }
  ];

  // Helper function to toggle languages
  const toggleLanguage = (language: string) => {
    setSelectedLanguages(prev => {
      if (prev.includes(language)) {
        // Don't remove if it's the last language
        if (prev.length === 1) return prev;
        return prev.filter(l => l !== language);
      } else {
        return [...prev, language];
      }
    });
  };

  // Get current result based on active language
  const currentResult = languageResults[activeLanguage];

  // Update the uniqueCategories effect to work with multiple languages
  useEffect(() => {
    if (currentResult) {
      const categories = currentResult.mnemonic_analysis.substitutions.substitution
        .map(sub => sub.category)
        .filter((value, index, self) => self.indexOf(value) === index);
      setUniqueCategories(["all", ...categories]);
    } else {
      setUniqueCategories(["all"]);
    }
  }, [currentResult]);

  // Filter substitutions based on active tab and language
  const filteredSubstitutions = currentResult?.mnemonic_analysis.substitutions.substitution.filter(sub => 
    activeTab === "all" || sub.category === activeTab
  ) || [];

  return (
    <main className="container mx-auto p-6 max-w-6xl">
      <TextSelectionActions
        chat={true}
        translate={true}
        smallDiplay={true} 
        actions={
          [
            ...basicActions,            
            actions.handleSynonyms,
            actions.handleExamples,
            actions.handleIPA,
            actions.handleAntonyms
          ]
        }
      >
        <h1 className="text-3xl font-bold mb-6">Mnemonic Generator</h1>
        
        {successMessage && (
          <Alert className="mb-4 bg-green-50 border-green-200">
            <Check className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Success</AlertTitle>
            <AlertDescription className="text-green-700">
              {successMessage}
            </AlertDescription>
          </Alert>
        )}
        
        {/* Two-column layout */}
        <div className="flex flex-col md:flex-row gap-6">
          {/* Left column - Pinned Mnemonics */}
          <div className="md:w-1/4 md:sticky md:top-6 md:self-start">
            {pinnedMnemonics.length > 0 ? (
              <PinnedMnemonicsPanel />
            ) : (
              <Card className="mb-6 w-full">
                <CardHeader className="pb-3">
                  <CardTitle>Pinned Mnemonics</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Pin mnemonics to keep them here for quick reference.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
          
          {/* Right column - Main content */}
          <div className="md:w-3/4">
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Generate Mnemonics</CardTitle>
                <CardDescription>
                  Enter a word to generate mnemonic substitutions that can help with memorization.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="flex gap-4 mb-4">
                    <Input
                      value={word}
                      onChange={(e) => setWord(e.target.value)}
                      placeholder="Enter a word (e.g., cantaloupe)"
                      className="flex-1"
                    />
                    <Button type="submit" disabled={loading}>
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        "Generate"
                      )}
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <Label htmlFor="language-select" className="mb-2 block">Substitution Languages</Label>
                      <div className="flex flex-wrap gap-2">
                        {languageOptions.map((lang) => (
                          <Button
                            key={lang.value}
                            variant={selectedLanguages.includes(lang.value) ? "default" : "outline"}
                            onClick={() => toggleLanguage(lang.value)}
                            className="text-sm"
                          >
                            {lang.label}
                          </Button>
                        ))}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">Languages used for mnemonic substitutions</p>
                    </div>
                    <div>
                      <Label htmlFor="count-select" className="mb-2 block">Number of Mnemonics</Label>
                      <select
                        id="count-select"
                        value={generationCount}
                        onChange={(e) => setGenerationCount(Number(e.target.value))}
                        className="w-full p-2 border rounded-md"
                      >
                        <option value="3">3</option>
                        <option value="5">5</option>
                        <option value="7">7</option>
                        <option value="10">10</option>
                      </select>
                      <p className="text-xs text-muted-foreground mt-1">Number of mnemonics to generate per category</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 mb-4">
                    <Checkbox 
                      id="advanced-mode" 
                      checked={advancedMode} 
                      onCheckedChange={(checked) => {
                        setAdvancedMode(checked === true);
                        if (checked === false) {
                          setSelectedCategories([]);
                        }
                      }}
                    />
                    <Label htmlFor="advanced-mode" className="flex items-center gap-2">
                      Advanced Mode
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs">
                            <p>Generate specific mnemonic categories alongside the initial response</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Label>
                  </div>
                  
                  {advancedMode && (
                    <div className="border rounded-md p-4 bg-slate-50 transition-all">
                      <h3 className="text-sm font-medium mb-3">Select specific mnemonic categories:</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {MNEMONIC_CATEGORIES.map((category) => (
                          <div key={category} className="flex items-start space-x-2">
                            <Checkbox 
                              id={`category-${category}`} 
                              checked={selectedCategories.includes(category)}
                              onCheckedChange={() => handleCategorySelect(category)}
                              className="mt-1"
                            />
                            <div>
                              <Label htmlFor={`category-${category}`} className="font-medium">{category}</Label>
                              <p className="text-xs text-muted-foreground">{CATEGORY_DESCRIPTIONS[category as keyof typeof CATEGORY_DESCRIPTIONS]}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                      {selectedCategories.length > 0 && (
                        <div className="mt-3 text-sm text-blue-600">
                          {selectedCategories.length} {selectedCategories.length === 1 ? 'category' : 'categories'} selected
                        </div>
                      )}
                    </div>
                  )}
                </form>
              </CardContent>
            </Card>

            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            {Object.keys(languageResults).length > 0 && (
              <div className="space-y-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center justify-between">
                      <span>Mnemonic Analysis for "{word}"</span>
                    </CardTitle>
                    <Tabs value={activeLanguage} onValueChange={setActiveLanguage} className="mt-4">
                      <TabsList className="mb-4 flex flex-wrap h-auto">
                        {Object.keys(languageResults).map(language => (
                          <TabsTrigger key={language} value={language} className="mb-1 capitalize">
                            {language}
                            {languageLoading.includes(language) && (
                              <Loader2 className="h-3 w-3 animate-spin ml-2" />
                            )}
                          </TabsTrigger>
                        ))}
                      </TabsList>
                    </Tabs>
                  </CardHeader>
                  <CardContent>
                    {currentResult ? (
                      <>
                        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-xl font-bold">{currentResult.mnemonic_analysis.input_word}</h3>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              onClick={() => speakText(currentResult.mnemonic_analysis.input_word)}
                              className="h-8 w-8 p-0"
                            >
                              <Volume2 className="h-4 w-4" />
                            </Button>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-500">IPA Pronunciation:</p>
                              <p className="font-mono">{currentResult.mnemonic_analysis.input_ipa}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">French Translation:</p>
                              <div className="flex items-center gap-2">
                                <p>{currentResult.mnemonic_analysis.input_french}</p>
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  onClick={() => speakFrench(currentResult.mnemonic_analysis.input_french)}
                                  className="h-6 w-6 p-0"
                                >
                                  <Volume2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>

                        {uniqueCategories.length > 1 && (
                          <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
                            <TabsList className="mb-4 flex flex-wrap h-auto">
                              {uniqueCategories.map(category => (
                                <TabsTrigger key={category} value={category} className="mb-1">
                                  {category === "all" ? "All Categories" : category}
                                  {category !== "all" && (
                                    <Badge variant="secondary" className="ml-2">
                                      {currentResult.mnemonic_analysis.substitutions.substitution.filter(s => s.category === category).length}
                                    </Badge>
                                  )}
                                </TabsTrigger>
                              ))}
                            </TabsList>
                          </Tabs>
                        )}

                        <div className="space-y-4">
                          {filteredSubstitutions.length > 0 ? (
                            filteredSubstitutions.map((sub, index) => renderSubstitutionCard(sub, index))
                          ) : (
                            <div className="text-center p-8 text-muted-foreground">
                              No mnemonics found for this category. Try generating more or selecting a different category.
                            </div>
                          )}
                        </div>
                      </>
                    ) : (
                      <div className="text-center p-8 text-muted-foreground">
                        {languageLoading.includes(activeLanguage) ? (
                          <div className="flex flex-col items-center gap-2">
                            <Loader2 className="h-8 w-8 animate-spin" />
                            <p>Generating mnemonics for {activeLanguage}...</p>
                          </div>
                        ) : (
                          <p>No results available for {activeLanguage}. Try generating mnemonics first.</p>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </TextSelectionActions>
    </main>
  );
}








