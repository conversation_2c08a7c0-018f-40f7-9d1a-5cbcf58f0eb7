// MarkdownTypewriter.tsx

import React, { useState, useEffect, useCallback, useMemo, JSX } from "react";

// Types and Interfaces
interface CursorStyle {
  shape: "block" | "line" | "underscore" | "custom";
  customContent?: string;
  width?: string;
  height?: string;
  color?: string;
  blinkSpeed?: number;
}

interface MarkdownTypewriterProps {
  content: string;
  typeSpeed?: number;
  initialDelay?: number;
  onComplete?: () => void;
  className?: string;
  cursor?: CursorStyle | false;
}

interface Segment {
  type: "text" | "html";
  content: string | React.ReactNode; // Changed from JSX.Element to React.ReactNode
  length: number;
}

interface TypewriterState {
  segments: Segment[];
  currentSegmentIndex: number;
  currentCharIndex: number;
  isStarted: boolean;
  showCursor: boolean;
}

// Predefined cursor styles
export const cursorPresets: Record<string, CursorStyle> = {
  block: {
    shape: "block",
    width: "w-2",
    height: "h-4",
    color: "bg-black",
    blinkSpeed: 530,
  },
  line: {
    shape: "line",
    width: "w-0.5",
    height: "h-4",
    color: "bg-black",
    blinkSpeed: 530,
  },
  underscore: {
    shape: "underscore",
    width: "w-2",
    height: "h-0.5",
    color: "bg-black",
    blinkSpeed: 530,
  },
};

// Markdown processing rules
interface MarkdownRule {
  pattern: RegExp;
  replacement: (match: string, ...args: string[]) => React.ReactNode | string; // Changed from JSX.Element to React.ReactNode
}

const markdownRules: MarkdownRule[] = [
  {
    // Headers
    pattern: /^(#{1,6})\s(.+)$/gm,
    replacement: (match: string, hashes: string, content: string) => {
      const level = hashes.length;
      const size =
        {
          1: "text-4xl",
          2: "text-3xl",
          3: "text-2xl",
          4: "text-xl",
          5: "text-lg",
          6: "text-base",
        }[level as 1 | 2 | 3 | 4 | 5 | 6] || "text-base";
  
      // 👇 Change here
      // Old (causing error):
      // const HeaderTag = `h${level}` as keyof React.ReactHTML;
      
      // New (fix):
      const HeaderTag = `h${level}` as keyof JSX.IntrinsicElements;
  
      return (
        <HeaderTag className={`font-bold ${size} mb-4`}>
          {content}
        </HeaderTag>
      );
    },
  },
  
  {
    // Bold
    pattern: /\*\*(.*?)\*\*/g,
    replacement: (match: string, content: string) => <strong>{content}</strong>,
  },
  {
    // Italic
    pattern: /\*(.*?)\*/g,
    replacement: (match: string, content: string) => <em>{content}</em>,
  },
  {
    // Code blocks
    pattern: /```([\s\S]*?)```/g,
    replacement: (match: string, code: string) => (
      <pre className="bg-gray-100 p-4 rounded-lg my-4 font-mono text-sm overflow-x-auto">
        {code.trim()}
      </pre>
    ),
  },
  {
    // Inline code
    pattern: /`([^`]+)`/g,
    replacement: (match: string, content: string) => (
      <code className="bg-gray-100 px-1 rounded text-sm font-mono">
        {content}
      </code>
    ),
  },
  {
    // Blockquotes
    pattern: /^>\s(.+)$/gm,
    replacement: (match: string, content: string) => (
      <blockquote className="border-l-4 border-gray-300 pl-4 py-2 my-4 italic">
        {content}
      </blockquote>
    ),
  },
  {
    // Lists
    pattern: /^[-*]\s(.+)$/gm,
    replacement: (match: string, content: string) => (
      <li className="ml-4">{content}</li>
    ),
  },
  {
    // Links
    pattern: /\[(.*?)\]\((.*?)\)/g,
    replacement: (match: string, text: string, url: string) => (
      <a href={url} className="text-blue-600 hover:underline">
        {text}
      </a>
    ),
  },
  {
    // Line breaks
    pattern: /\n/g,
    replacement: () => <br />,
  },
];

// Cursor Component
const Cursor: React.FC<{ style: CursorStyle; visible: boolean }> = ({
  style,
  visible,
}) => {
  if (style.shape === "custom" && style.customContent) {
    return (
      <span
        className={`inline-block ml-1 ${visible ? "opacity-100" : "opacity-0"}`}
        style={{
          transition: `opacity ${style.blinkSpeed || 530}ms`,
          color: style.color?.replace("bg-", "text-") || "text-black",
        }}
      >
        {style.customContent}
      </span>
    );
  }

  const cursorClass = [
    "inline-block ml-1",
    style.width || "w-2",
    style.height || "h-4",
    style.color || "bg-black",
    visible ? "opacity-100" : "opacity-0",
  ].join(" ");

  return <span className={cursorClass} aria-hidden="true" />;
};

// Utility functions
const mergeCursorStyles = (customStyle: Partial<CursorStyle>): CursorStyle => {
  const baseStyle = cursorPresets[customStyle.shape || "block"];
  return { ...baseStyle, ...customStyle };
};

/**
 * Splits `text` into an array of strings & React elements by applying all markdown rules.
 * Example: ["Some ", <strong>bold</strong>, " text"]
 */
const processMarkdown = (text: string): (string | React.ReactNode)[] => {
  // Start with one big string
  let processed: (string | React.ReactNode)[] = [text];

  // Sequentially apply each rule
  markdownRules.forEach((rule) => {
    const newOutput: (string | React.ReactNode)[] = [];

    // Walk through the current array of pieces
    processed.forEach((piece) => {
      if (typeof piece === "string") {
        // We apply the pattern to this string
        let remaining = piece;
        let match: RegExpExecArray | null;
        let lastIndex = 0;

        while ((match = rule.pattern.exec(piece)) !== null) {
          // Add text before the match
          if (match.index > lastIndex) {
            newOutput.push(remaining.slice(0, match.index - lastIndex));
          }

          // Capture the matched portion
          const matched = match[0];
          const captures = match.slice(1); // everything after group(0)
          newOutput.push(rule.replacement(matched, ...captures));

          // Update `remaining` and `lastIndex`
          const endOfMatch = match.index + matched.length;
          remaining = remaining.slice(endOfMatch - lastIndex);
          lastIndex = endOfMatch;
        }

        // Add leftover text if any
        if (remaining.length > 0) {
          newOutput.push(remaining);
        }
      } else {
        // Already a React element, just push as is
        newOutput.push(piece);
      }
    });

    processed = newOutput;
  });

  return processed;
};

// Main Component
const MarkdownTypewriter: React.FC<MarkdownTypewriterProps> = ({
  content,
  typeSpeed = 50,
  initialDelay = 500,
  onComplete,
  className = "",
  cursor = cursorPresets.block,
}) => {
  const [state, setState] = useState<TypewriterState>({
    segments: [],
    currentSegmentIndex: 0,
    currentCharIndex: 0,
    isStarted: false,
    showCursor: true,
  });

  /**
   * Convert the final (parsed) array into segments (where text is 1 segment per character,
   * and each JSX element is 1 "character").
   */
  const processContent = useCallback((markdown: string): Segment[] => {
    const processed = processMarkdown(markdown);

    const segments: Segment[] = processed.map((part) => {
      if (typeof part === "string") {
        return {
          type: "text",
          content: part,
          length: part.length,
        };
      } else {
        // For a React element, treat it as length=1
        return {
          type: "html",
          content: part,
          length: 1,
        };
      }
    });

    return segments;
  }, []);

  // Parse and set up segments whenever `content` changes
  useEffect(() => {
    const processedSegments = processContent(content);
    setState({
      segments: processedSegments,
      currentSegmentIndex: 0,
      currentCharIndex: 0,
      isStarted: false,
      showCursor: true,
    });
  }, [content, processContent]);

  // Delay the start of the typing
  useEffect(() => {
    if (state.segments.length === 0) return;

    const startTimeout = setTimeout(() => {
      setState((prev) => ({
        ...prev,
        isStarted: true,
      }));
    }, initialDelay);

    return () => clearTimeout(startTimeout);
  }, [state.segments, initialDelay]);

  // Typing effect
  useEffect(() => {
    if (!state.isStarted || state.segments.length === 0) return;

    const currentSegment = state.segments[state.currentSegmentIndex];
    if (!currentSegment) return;

    const isLastSegment =
      state.currentSegmentIndex === state.segments.length - 1;
    const isSegmentComplete = state.currentCharIndex >= currentSegment.length;

    // If we've fully typed the current segment
    if (isSegmentComplete) {
      // If it's also the very last segment, we're done
      if (isLastSegment) {
        onComplete?.();
        return;
      }

      // Otherwise, move on to the next segment
      const timeout = setTimeout(() => {
        setState((prev) => ({
          ...prev,
          currentSegmentIndex: prev.currentSegmentIndex + 1,
          currentCharIndex: 0,
        }));
      }, typeSpeed);

      return () => clearTimeout(timeout);
    }

    // Continue typing the current segment
    const typingTimeout = setTimeout(() => {
      setState((prev) => ({
        ...prev,
        currentCharIndex: prev.currentCharIndex + 1,
      }));
    }, typeSpeed);

    return () => clearTimeout(typingTimeout);
  }, [state, typeSpeed, onComplete]);

  // Cursor blinking
  useEffect(() => {
    if (!cursor) return;

    const cursorStyle =
      typeof cursor === "object" ? mergeCursorStyles(cursor) : cursorPresets.block;
    const blinkSpeed = cursorStyle.blinkSpeed || 530;

    const cursorInterval = setInterval(() => {
      setState((prev) => ({
        ...prev,
        showCursor: !prev.showCursor,
      }));
    }, blinkSpeed);

    return () => clearInterval(cursorInterval);
  }, [cursor]);

  // As we type, build an array of partially typed content
  const renderedChildren = useMemo<(string | React.ReactNode)[]>(() => {
    const out: (string | React.ReactNode)[] = [];

    state.segments.forEach((segment, index) => {
      // If this segment hasn't been reached yet, skip
      if (index > state.currentSegmentIndex) return;

      // If fully typed an earlier segment, push the entire segment
      if (index < state.currentSegmentIndex) {
        out.push(segment.content);
      }
      // Otherwise, we partially type the current segment
      else {
        if (segment.type === "text") {
          const typedSoFar = (segment.content as string).slice(
            0,
            state.currentCharIndex
          );
          if (typedSoFar) out.push(typedSoFar);
        } else {
          // If it's a React element, once we 'type' it, we show it fully
          // If `currentCharIndex` >= 1, means we've typed the "character"
          if (state.currentCharIndex >= 1) {
            out.push(segment.content);
          }
        }
      }
    });

    return out;
  }, [
    state.segments,
    state.currentSegmentIndex,
    state.currentCharIndex,
  ]);

  // Check if typing is complete
  const isTypingComplete = useMemo(() => {
    if (state.segments.length === 0) return false;
    const lastIdx = state.segments.length - 1;
    return (
      state.currentSegmentIndex === lastIdx &&
      state.currentCharIndex >= state.segments[lastIdx].length
    );
  }, [state]);

  return (
    <div
      className={`markdown-typewriter max-w-3xl mx-auto ${className}`}
      // Ensure text selection is allowed
      style={{ userSelect: "text" }} // Or use className="select-text" if using Tailwind
    >
      {/* 
        Using a normal div (rather than inline) so it's easier to select across lines.
        We just map our array of partially typed segments (strings & elements). 
      */}
      <div>
        {renderedChildren.map((child, i) => {
          if (typeof child === "string") {
            // String => just render as text
            return <React.Fragment key={i}>{child}</React.Fragment>;
          }
          // Element => render directly
          return React.cloneElement(child as React.ReactElement, { key: i });
        })}
      </div>

      {/* Show the cursor if still typing */}
      {cursor && !isTypingComplete && (
        <Cursor
          style={
            typeof cursor === "object"
              ? mergeCursorStyles(cursor)
              : cursorPresets.block
          }
          visible={state.showCursor}
        />
      )}
    </div>
  );
};

export default MarkdownTypewriter;
