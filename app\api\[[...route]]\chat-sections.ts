import { Hono } from "hono";
import { PrismaClient } from '@prisma/client';

const app = new Hono();
const prisma = new PrismaClient();

app.get("/", async (c) => {
  try {
    const chatSections = await prisma.chatSection.findMany({
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        grammarRuleSections: {
          include: {
            grammarRule: true, // Include the actual grammar rule details
          },
        },
        messages: {
          orderBy: {
            createdAt: 'asc',
          },
        },
        chatSectionTopics: {
          include: {
            customChatSetting: true, // Include the actual topic details
          },
        },
      },
    });
    return c.json(chatSections);
  } catch (error) {
    console.error("Error fetching chat sections:", error);
    return c.json({ error: "Failed to fetch chat sections." }, 500);
  }
});

app.get("/:id", async (c) => {
  const { id } = c.req.param();
  try {
    const chatSection = await prisma.chatSection.findUnique({
      where: { id },
      include: {
        grammarRuleSections: {
          include: {
            grammarRule: true,
          },
        },
        messages: {
          orderBy: {
            createdAt: 'asc',
          },
        },
        chatSectionTopics: {
          include: {
            customChatSetting: true,
          },
        },
      },
    });

    if (!chatSection) {
      return c.json({ error: "Chat section not found." }, 404);
    }

    return c.json(chatSection);
  } catch (error) {
    console.error(`Error fetching chat section ${id}:`, error);
    return c.json({ error: `Failed to fetch chat section ${id}.` }, 500);
  }
});

app.delete("/:id", async (c) => {
  const { id } = c.req.param();
  try {
    await prisma.chatSection.delete({
      where: { id },
    });
    return c.json({ message: "Chat section deleted successfully." });
  } catch (error) {
    console.error(`Error deleting chat section ${id}:`, error);
    return c.json({ error: `Failed to delete chat section ${id}.` }, 500);
  }
});

export default app;