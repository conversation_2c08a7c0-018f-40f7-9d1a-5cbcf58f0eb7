import {
  AIMessage,
  BaseMessage,
  SystemMessage,
} from "@langchain/core/messages";
import { tool, DynamicTool } from "@langchain/core/tools";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { StateGraph, START, END, Annotation } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";
import { MemorySaver } from "@langchain/langgraph";
import { z } from "zod";
import { sp_IPA, sp_WD } from "./prompt";
import {
  nounsGen,
  nouns,
  verbsGen,
  verbs,
  adjectivesGen,
  adjectives,
  adverbsGen,
  adverbs,
  newThemeDiscover,
  mnemonic as mnemonicSchema,
} from "./schemas";
import { PromptTemplate } from "@langchain/core/prompts";


//TODO expand this to be structured with classes

const AgentState = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [new SystemMessage(sp_IPA)],
  }),
});

export class Agent {
  model: ChatOpenAI;
  State: typeof AgentState;
  tools: DynamicTool[];

  constructor({
    model,
    State,
    tools,
  }: {
    model: ChatOpenAI;
    State: typeof AgentState;
    tools: DynamicTool[];
  }) {
    this.model = model;
    this.State = State;
    this.tools = tools;
  }

  create() {
    const toolNode = new ToolNode(this.tools);

    const shouldContinue = (state: typeof AgentState.State) => {
      const lastMessage = state.messages[state.messages.length - 1];

      if (lastMessage && !(lastMessage as AIMessage).tool_calls?.length) {
        return END;
      }

      return "action";
    };

    const agentNode = async (state: typeof AgentState.State) => {
      const messages = state.messages;
      console.log(messages);

      try {
        const response = await this.model.invoke(messages);
        return { messages: [response] };
      } catch (err) {
        console.log(err);
        return { messages: [new AIMessage("error")] };
      }
    };

    const workflow = new StateGraph(this.State)
      .addNode("agent", agentNode)
      .addNode("action", toolNode)
      .addEdge(START, "agent")
      .addEdge("action", "agent")
      .addConditionalEdges("agent", shouldContinue);

    const agent = workflow.compile({
      checkpointer: memory,
    });

    return agent;
  }
}

const memory = new MemorySaver();

const openai = new ChatOpenAI({
  model: "gpt-4o",
});

const AgentWordDiscover = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [new SystemMessage(sp_WD)],
  }),
});

const searchtool = tool(
  (input) => {
    return "search result";
  },
  {
    name: "searchtool",
    description: "search tool",
    schema: z.string().describe("search to be made"),
  }
);

//done
const toolNode = new ToolNode([searchtool]);

const shouldContinue = (state: typeof AgentState.State) => {
  const lastMessage = state.messages[state.messages.length - 1];

  if (lastMessage && !(lastMessage as AIMessage).tool_calls?.length) {
    return END;
  }

  return "action";
};

const agentNode = async (state: typeof AgentState.State) => {
  const messages = state.messages;
  console.log(messages);

  try {
    const response = await openai.invoke(messages);
    return { messages: [response] };
  } catch (err) {
    console.log(err);
    return { messages: [new AIMessage("error")] };
  }
};

const workflow = new StateGraph(AgentState)
  .addNode("agent", agentNode)
  .addNode("action", toolNode)
  .addEdge(START, "agent")
  .addEdge("action", "agent")
  .addConditionalEdges("agent", shouldContinue);

export const agent = workflow.compile({
  checkpointer: memory,
});

const workflowDiscover = new StateGraph(AgentWordDiscover)
  .addNode("agent", agentNode)
  .addNode("action", toolNode)
  .addEdge(START, "agent")
  .addEdge("action", "agent")
  .addConditionalEdges("agent", shouldContinue);

export const agentDiscover = workflowDiscover.compile({
  checkpointer: memory,
});

//--------------------------------------------------------------------------//
//TODO create class

//---Nouns

const GraphState = Annotation.Root({
  theme: Annotation<string>,
  nouns: Annotation<z.infer<typeof nouns> | string>,
  verbs: Annotation<z.infer<typeof verbs> | string>,
  adverbs: Annotation<z.infer<typeof adverbs> | string>,
  adjectives: Annotation<z.infer<typeof adjectives> | string>,
  subCategory: Annotation<string[] | string>,
});

const mnemonicModel = new ChatOpenAI({
  model: "gpt-4o",
}).withStructuredOutput(
  z.object({
    hightFrequency: mnemonicSchema,
    middleFrequency: mnemonicSchema,
  })
);

const promptMnemonic =
  PromptTemplate.fromTemplate(`You are an AI memory coach specializing in maximizing word retention using mnemonic techniques. Your goal is to help users memorize a given list of words by applying the **Key Word Method** and **Association and Visualization techniques**.
    you will receive an object containing words with higth frequency use and middleFrequncy use. provide for both of them the mnemonic following the this structure.
    mnemonic: {{
        hightFrequency: {{
          keywordMethod: [ //arrays ],
          associationAndVisualization: [ //arrays ]
        }},
        middleFrequency: {{
          keywordMethod: [ //arrays ],
          associationAndVisualization: [ //arrays ]
            }}
      }}
    }}
    input:{input}
`);
const mnemonicChain = promptMnemonic.pipe(mnemonicModel);

//---start node

const startNode = (state: typeof GraphState.State) => {
  return state;
};

//---subcategory

const themeExtender = new ChatOpenAI({
  model: "gpt-4o",
}).withStructuredOutput(
  z.object({
    subCategory: z
      .array(z.string())
      .max(4)
      .describe("The subcategory of the theme being described."),
  })
);

const extenderAgentNode = async (state: typeof GraphState.State) => {
  console.log(`--EXTENDER NODE--`);

  const prompt =
    PromptTemplate.fromTemplate(`You are an AI language assistant designed to help users expand their vocabulary based on specific themes. When provided with a theme, your task is to identify up to four relevant subcategories related to that theme. For each subcategory, generate a list of associated words that aid in vocabulary expansion.
        input:{input}
        `);
  const chain = prompt.pipe(themeExtender);
  let success = false;
  let attempts = 0;

  while (!success && attempts < 5) {
    try {
      attempts += 1; 
      const response = await chain.invoke({ input: state.theme });

      console.log(`### subCategory : ${JSON.stringify(response, null, 2)} `);
      console.log(`--END EXTENDER  theme: ${JSON.stringify(state.theme)}`);
      success = true;
      return { subCategory: response.subCategory };
    } catch (err) {
      console.log(err);
    }
  }
  return { subCategory: `error generating nouns for the theme ${state.theme}` };
};

//---nouns

const theDetaillerNouns = new ChatOpenAI({
  model: "gpt-4o",
}).withStructuredOutput(nounsGen);

const nounsAgentNode = async (state: typeof GraphState.State) => {
  console.log(`---NOUNS NODE---`);

  const prompt =
    PromptTemplate.fromTemplate(`You are an AI language assistant designed to help users expand their vocabulary by providing nouns associated with specific themes. When given a theme, your task is to generate a comprehensive list of relevant and diverse nouns related to that theme.
    provide a minimum of 6 nouns and 5 collocations for boths highFrequency and middleFrequency.
    here is the theme:{input}
    `);
  const chain = prompt.pipe(theDetaillerNouns);
  let mnemonic;

  let response;

  try {
    let successResponse = false;

    let attemptsResponse = 0;

    while (!successResponse && attemptsResponse < 5) {
      attemptsResponse += 1; 
      try {
        response = await chain.invoke({ input: state.theme });

        console.log(
          `### nouns : ${JSON.stringify(
            response,

            null,

            2
          )}  ### theme: ${JSON.stringify(state.theme, null, 2)}`
        );

        successResponse = true;
      } catch (err) {
        console.log(err);
      }
    }

    let successMn = false;

    let attemptsMn = 0;

    while (!successMn && attemptsMn < 5) {
      attemptsMn += 1;
      try {
        mnemonic = await mnemonicChain.invoke({
          input: JSON.stringify(response, null, 2),
        });

        successMn = true;
      } catch (err) {
        console.log(err);
      }
    }

    const finalRes = {
      nouns: response,
      mnemonic,
    };

    console.log(
      `### mnemonic : ${JSON.stringify(
        mnemonic,
        null,
        2
      )}  ### theme: ${JSON.stringify(state.theme, null, 2)}`
    );

    console.log(
      `--END NOUNS ### theme: ${JSON.stringify(state.theme, null, 2)}`
    );

    return { nouns: finalRes };
  } catch (err) {
    console.log(err);
  }
  return { nouns: `error generating nouns for the theme ${state.theme}` };
};

//---Verbs

const theDetaillerVerbs = new ChatOpenAI({
  model: "gpt-4o",
}).withStructuredOutput(verbsGen);

const verbsAgentNode = async (state: typeof GraphState.State) => {
  console.log(`---VERBS NODE---`);

  const prompt =
    PromptTemplate.fromTemplate(`You are an AI language assistant designed to help users expand their vocabulary by providing verbs associated with specific themes. When given a theme, your task is to generate a comprehensive list of relevant and diverse verbs (with or without prepositions) related to that theme.
        provide a minimum of 6 verbs and 5 collocations for boths highFrequency and middleFrequency. Please ensure that each verb is provide with his associated preposition if it exist.
    here is the theme:{input}
    `);
  const chain = prompt.pipe(theDetaillerVerbs);

  let mnemonic;
  let response;

  try {
    let successResponse = false;
    let attemptsResponse = 0;

    while (!successResponse && attemptsResponse < 5) {
      attemptsResponse += 1;
      try {
        response = await chain.invoke({ input: state.theme });
        console.log(
          `### verbs : ${JSON.stringify(response, null, 2)}  ### theme: ${
            state.theme
          }`
        );

        successResponse = true;
      } catch (err) {
        console.log(err);
      }
    }

    let successMn = false;
    let attemptsMn = 0;

    while (!successMn && attemptsMn < 5) {
      attemptsMn += 1;
      try {
        mnemonic = await mnemonicChain.invoke({
          input: JSON.stringify(response, null, 2),
        });
        successMn = true;
      } catch (err) {
        console.log(err);
      }
    }

    const finalRes = {
      verbs: response,
      mnemonic,
    };

    console.log(
      `### mnemonic : ${JSON.stringify(mnemonic)}  ### theme: ${JSON.stringify(
        state.theme
      )}`
    );
    console.log(`--END VERBS ### theme: ${JSON.stringify(state.theme)}`);
    return { verbs: finalRes };
  } catch (err) {
    console.log(err);
  }

  return { verbs: `error generating verbs for the theme ${state.theme}` };
};

//---adjectives

const theDetaillerAdjs = new ChatOpenAI({
  model: "gpt-4o",
}).withStructuredOutput(adjectivesGen);

const adjectivesAgentNode = async (state: typeof GraphState.State) => {
  console.log(`---ADJECTIVES NODE---`);

  const prompt =
    PromptTemplate.fromTemplate(`You are an AI language assistant designed to help users expand their vocabulary by providing adjectives associated with specific themes. When given a theme, your task is to generate a comprehensive list of relevant and diverse adjectives related to that theme.
        provide a minimum of 6 adjectives and 5 collocations for boths highFrequency and middleFrequency
    here is the theme:{input}
    `);
  const chain = prompt.pipe(theDetaillerAdjs);

  let mnemonic;

  let response;

  try {
    let successResponse = false;

    let attemptsResponse = 0;

    while (!successResponse && attemptsResponse < 5) {
      attemptsResponse += 1;
      try {
        response = await chain.invoke({ input: state.theme });

        console.log(
          `### adjectives : ${JSON.stringify(
            response,
            null,
            2
          )} ### theme: ${JSON.stringify(state.theme)}`
        );

        successResponse = true;
      } catch (err) {
        console.log(err);
      }
    }

    let successMn = false;

    let attemptsMn = 0;

    while (!successMn && attemptsMn < 5) {
      attemptsMn += 1;
      try {
        mnemonic = await mnemonicChain.invoke({
          input: JSON.stringify(response, null, 2),
        });

        successMn = true;
      } catch (err) {
        console.log(err);
      }
    }

    const finalRes = {
      adjectives: response,
      mnemonic,
    };

    console.log(
      `### mnemonic : ${JSON.stringify(mnemonic)} ### theme: ${state.theme}`
    );

    console.log(`--END ADJECTIVES ### theme: ${JSON.stringify(state.theme)}`);

    return { adjectives: finalRes };
  } catch (err) {
    console.log(err);
  }

  return {
    adjectives: `error generating adjectives for the theme ${state.theme}`,
  };
};

//---adverbs

const theDetaillerAdvs = new ChatOpenAI({
  model: "gpt-4o",
}).withStructuredOutput(adverbsGen);

const adverbsAgentNode = async (state: typeof GraphState.State) => {
  console.log(`---ADVERBS NODE---`);

  const prompt =
    PromptTemplate.fromTemplate(`You are an AI language assistant designed to help users expand their vocabulary by providing adverbs associated with specific themes. When given a theme, your task is to generate a comprehensive list of relevant and diverse adverbs related to that theme.
        provide a minimum of 6 adverbs and 5 collocations for boths highFrequency and middleFrequency
    here is the theme:{input}
    `);
  const chain = prompt.pipe(theDetaillerAdvs);
  let mnemonic;

  let response;

  try {
    let successResponse = false;

    let attemptsResponse = 0;

    while (!successResponse && attemptsResponse < 5) {
      attemptsResponse += 1;
      try {
        response = await chain.invoke({ input: state.theme });

        console.log(
          `### adverbs : ${JSON.stringify(response, null, 2)}  ### theme: ${
            state.theme
          }`
        );

        successResponse = true;
      } catch (err) {
        console.log(err);
      }
    }

    let successMn = false;

    let attemptsMn = 0;

    while (!successMn && attemptsMn < 5) {
      attemptsMn += 1;
      try {
        mnemonic = await mnemonicChain.invoke({
          input: JSON.stringify(response, null, 2),
        });

        successMn = true;
      } catch (err) {
        console.log(err);
      }
    }

    const finalRes = {
      adverbs: response,

      mnemonic,
    };

    console.log(
      `### mnemonic : ${JSON.stringify(mnemonic)}  ### theme: ${state.theme}`
    );

    console.log(`--END ADVERBS ### theme: ${JSON.stringify(state.theme)}`);

    return { adverbs: finalRes };
  } catch (err) {
    console.log(err);
  }

  return {
    adverbs: `error generating adverbs for the ### theme: ${JSON.stringify(
      state.theme
    )}`,
  };
};

const endGraph = (state: typeof GraphState.State) => {
  console.log(`--- END ---`);
  return state;
};

const secondGraph = new StateGraph(GraphState)
  .addNode("nounsAgentNode", nounsAgentNode)
  .addNode("verbsAgentNode", verbsAgentNode)
  .addNode("adjectivesAgentNode", adjectivesAgentNode)
  .addNode("adverbsAgentNode", adverbsAgentNode)
  .addNode("extenderAgentNode", extenderAgentNode)
  .addNode("startNode", startNode)
  .addNode("end", endGraph)
  .addEdge(START, "startNode")
  .addEdge("startNode", "verbsAgentNode")
  .addEdge("verbsAgentNode", "end")
  .addEdge("startNode", "extenderAgentNode")
  .addEdge("extenderAgentNode", "end")
  .addEdge("startNode", "nounsAgentNode")
  .addEdge("nounsAgentNode", "end")
  .addEdge("startNode", "adjectivesAgentNode")
  .addEdge("adjectivesAgentNode", "end")
  .addEdge("startNode", "adverbsAgentNode")
  .addEdge("adverbsAgentNode", "end")
  .addEdge("end", END);

export const themeAgent = secondGraph.compile();

//---next level
