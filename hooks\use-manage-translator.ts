// src/store/useTranslatorStore.ts

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { flexibleTranslationSchema } from "@/lib/schemas";
import {z} from "zod"

// Define the shape of a single translation
type IResponseData = z.infer<typeof flexibleTranslationSchema>

// Define the shape of the response data


// Define the state and actions
interface TranslatorState {
  selectedLang: string;
  inputText: string;
  responseData: IResponseData | null;
  loading: boolean;
  errorMsg: string;
  
  // Actions
  setSelectedLang: (lang: string) => void;
  setInputText: (text: string) => void;
  setResponseData: (data: IResponseData | null) => void;
  setLoading: (isLoading: boolean) => void;
  setErrorMsg: (msg: string) => void;
  handleTranslate: () => Promise<void>;
}

export const useManageTranslator = create<TranslatorState>()(
  devtools((set, get) => ({
    selectedLang: "english",
    inputText: "",
    responseData: null,
    loading: false,
    errorMsg: "",

    setSelectedLang: (lang: string) => set({ selectedLang: lang }),
    setInputText: (text: string) => set({ inputText: text }),
    setResponseData: (data: IResponseData | null) => set({ responseData: data }),
    setLoading: (isLoading: boolean) => set({ loading: isLoading }),
    setErrorMsg: (msg: string) => set({ errorMsg: msg }),

    handleTranslate: async () => {
      set({ loading: true, errorMsg: "", responseData: null });
      const { inputText, selectedLang } = get();

      try {
        const res = await fetch("/api/chat/flexible-translate", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            textToTranslate: inputText,
            targetLanguages: [selectedLang],
          }),
        });

        if (!res.ok) {
          throw new Error("Translation request failed.");
        }

        const data: IResponseData = await res.json();
        set({ responseData: data });
      } catch (err: any) {
        set({ errorMsg: err.message || "An error occurred." });
      } finally {
        set({ loading: false });
      }
    },
  }))
);
