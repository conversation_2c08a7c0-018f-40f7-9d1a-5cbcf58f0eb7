import { <PERSON>o } from "hono";
import {
  HumanMessage,
  SystemMessage,
  BaseMessage,
} from "@langchain/core/messages";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { Annotation } from "@langchain/langgraph";
import { tool } from "@langchain/core/tools";
import { ChatOpenAI } from "@langchain/openai";
import { ChatTogetherAI } from "@langchain/community/chat_models/togetherai";
import { Agent } from "@/lib/ai-functions";
import { z } from "zod";
import { ChatMistralAI } from "@langchain/mistralai";
import { XMLParser } from "fast-xml-parser";
import { exemples_substitutions } from "@/lib/prompt";

const searchtool = tool(
  (input) => {
    return "search result";
  },
  {
    name: "searchtool",
    description: "search tool",
    schema: z.string().describe("search to be made"),
  }
);


const MNEMONIC_SYSTEM_PROMPT = `You are an AI expert in generating mnemonic substitution words. Your task is to take an input word and identify one or more words or short phrases that sound similar to the input word or parts of it. The goal is to find potential building blocks for vivid and memorable mnemonics.

You will generate substitutions primarily in {language}, focusing on words that sound similar to the input word  in the target language : {language}.

For each identified substitution, you will also determine the primary phonological or orthographic "tweak" used to create it based on the following guide. However, **do not limit yourself solely to these categories; feel free to identify other relevant phonetic or orthographic relationships if they provide a strong mnemonic potential.**

**Mnemonic Category Guide:**

## 1. Homophonic Replacement

Entire words or morphemes are swapped with near-soundalikes.

- **Chicago ("chicken in a car, and the car can't go")**  
    • "Chi-ca-go" ⇄ "chicken … car … go"
    
- **Temptation ("Lead us not into Penn Station")**  
    • "temp-ta-tion" ⇄ "Penn Sta-tion"
    

**Why it works:** Entire stressed syllables match in sound, so the brain keys in on the familiar rhythm.

---

## 2. Blending & Resegmentation

Original multi-word phrases collapse into a single word (or vice versa).

- **Can't elope → cantaloupe**  
    • Can't / elope → can-ta-lope
    
- **Pennsylvania → pencil + vein-nia**  
    • Pen syl-va-nia → pen-cil + "vein-nia"
    

**Why it works:** By regrouping (re-segmenting) syllables, you create a seamless bridge between two familiar strings.

---

## 3. Insertion or Deletion of a Segment

One phoneme or syllable is added or dropped to align the sounds.

- **Alaska ("baked Alaska")**  
    • bak-t ə-laska (insertion of /t/)
    
- **Arkansas ("saw ark")**  
    • /ˈɑːrkənˌsɔː/ → /sɔː ɑːrk/ (drop the final "sans" syllable)
    

**Why it works:** The added or removed sound ensures an exact or near-exact homophone, reinforcing the pun.

---

## 4. Metathesis & Transposition

Syllables or phonemes swap positions.

- **Montana ("mountain")**  
    • /ˈmɒn.tæ.nə/ ⇄ /ˈmaʊn.tən/
    
- **Vermont ("vermin mount")**  
    • /ver ˈmɒnt/ ⇄ /ˈvɜːr.mɪn maʊnt/
    

**Why it works:** The swap is subtle enough to trigger recognition but novel enough to stick.

---

## 5. Semantic Elaboration

A straightforward homophone is dressed up with a vivid mental image.

- **Texas ("taxes")**  
    • Tax-iz → a towering pile of tax forms
    
- **Georgia ("George")**  
    • dʒɔːrdʒə → a man named George wearing a peach-themed hat
    

**Why it works:** Simple sound-match, but the strong visual scaffold cements memory.

---

## 6. Phonotactic Stretching

The original word's phonemes are kept in order but stretched or "stuttered" across extra syllables.

- **Mississippi ("Mrs. sip")**  
    • ˌmɪsɪˈsɪpi → Mrs. sip + trailing "i"
    
- **Minnesota ("mini soda")**  
    • ˌmɪnɪˈsoʊdə → mini- soda
    

**Why it works:** The repeated vowel-consonant frames create a rhythmic echo.

---

## 7. Alliteration & Rhythmic Echo

Emphasis on matching initial consonants or rhythmic feet.

- **Massachusetts ("mass chew sit")**  
    • /ˌmæsəˈtʃuːsɪts/ → mass-chew-sit
    
- **Wisconsin ("wise cousin")**  
    • /wɪsˈkɒnsɪn/ → wise-cousin
    

**Why it works:** Shared onsets (m-, w-) plus matching meter make the pun especially sticky.

---

### Putting It All Together

Most of your mnemonics combine two or more of these mechanisms:

- **Cantaloupe** uses **blending** (can't elope → cantaloupe) plus **homophonic replacement**.
    
- **Chicago** blends **homophony** ("Chi-ca-go" ⇄ "chicken in a car, and the car can't go") with **semantic elaboration**.
    
- **Alaska** shows **insertion** ("baked" → /beɪkt/) atop a direct homophone.
    

By consciously mixing phonetic tricks (insertion/deletion, metathesis) with vivid imagery, you maximize both the sound-based cue and the visual memory anchor—making each state name (or word) truly unforgettable.

**Output Format:**

Your response MUST be formatted as XML according to the following structure:

<mnemonic_analysis>
  <input_word>INPUT WORD</input_word>
  <input_ipa>/IPA TRANSCRIPTION OF INPUT WORD/</input_ipa>
  <input_french>FRENCH TRANSLATION OF INPUT WORD</input_french>
  <substitutions>
    <substitution>
      <word>SUBSTITUTE WORD 1</word>
      <ipa>/IPA TRANSCRIPTION OF SUBSTITUTE WORD 1/</ipa>
      <french>FRENCH TRANSLATION OF SUBSTITUTE WORD 1</french>
      <category>PRIMARY MNEMONIC CATEGORY 1</category>
      <notes>OPTIONAL NOTES ON THE TECHNIQUE OR OTHER OBSERVATIONS</notes>
    </substitution>
    <substitution>
      <word>SUBSTITUTE WORD 2</word>
      <ipa>/IPA TRANSCRIPTION OF SUBSTITUTE WORD 2/</ipa>
      <french>FRENCH TRANSLATION OF SUBSTITUTE WORD 2</french>
      <category>PRIMARY MNEMONIC CATEGORY 2</category>
      <notes>OPTIONAL NOTES ON THE TECHNIQUE OR OTHER OBSERVATIONS</notes>
    </substitution>
  </substitutions>
</mnemonic_analysis>

other exemples : ${exemples_substitutions}

`;

// Additional prompt for generating specific category mnemonics
const CATEGORY_SPECIFIC_PROMPT = `You are an AI expert in generating mnemonic substitution words. Your task is to take an input word and generate ONLY mnemonics that use the specified category technique.

Focus exclusively on the requested category: {category}
Generate substitutions primarily in {language}.
Generate exactly {count} high-quality mnemonic substitutions using ONLY this technique.

Here's a reminder of what this category involves:

{category_description}

### Putting It All Together

Most of your mnemonics combine two or more of these mechanisms:

- **Cantaloupe** uses **blending** (can't elope → cantaloupe) plus **homophonic replacement**.
    
- **Chicago** blends **homophony** ("Chi-ca-go" ⇄ "chicken in a car, and the car can't go") with **semantic elaboration**.
    
- **Alaska** shows **insertion** ("baked" → /beɪkt/) atop a direct homophone.
    

By consciously mixing phonetic tricks (insertion/deletion, metathesis) with vivid imagery, you maximize both the sound-based cue and the visual memory anchor—making each state name (or word) truly unforgettable.

**Output Format:**

Your response MUST be formatted as XML according to the following structure:

<mnemonic_analysis>
  <input_word>INPUT WORD</input_word>
  <input_ipa>/IPA TRANSCRIPTION OF INPUT WORD/</input_ipa>
  <input_french>FRENCH TRANSLATION OF INPUT WORD</input_french>
  <substitutions>
    <substitution>
      <word>SUBSTITUTE WORD 1</word>
      <ipa>/IPA TRANSCRIPTION OF SUBSTITUTE WORD 1/</ipa>
      <french>FRENCH TRANSLATION OF SUBSTITUTE WORD 1</french>
      <category>{category}</category>
      <notes>OPTIONAL NOTES ON THE TECHNIQUE OR OTHER OBSERVATIONS</notes>
    </substitution>
    <substitution>
      <word>SUBSTITUTE WORD 2</word>
      <ipa>/IPA TRANSCRIPTION OF SUBSTITUTE WORD 2/</ipa>
      <french>FRENCH TRANSLATION OF SUBSTITUTE WORD 2</french>
      <category>{category}</category>
      <notes>OPTIONAL NOTES ON THE TECHNIQUE OR OTHER OBSERVATIONS</notes>
    </substitution>
  </substitutions>
</mnemonic_analysis>`;

// Category descriptions for specific prompts
const CATEGORY_DESCRIPTIONS = {
  "Homophonic Replacement":
    "Entire words or morphemes are swapped with near-soundalikes. Example: Chicago ('chicken in a car, and the car can't go') where 'Chi-ca-go' sounds like 'chicken … car … go'.",
  "Blending & Resegmentation ":
    "Original multi-word phrases collapse into a single word (or vice versa). Example: Can't elope → cantaloupe, where 'Can't / elope' becomes 'can-ta-lope'.",
  "Insertion or Deletion of a Segment":
    "One phoneme or syllable is added or dropped to align the sounds. Example: Alaska ('baked Alaska') where 'bak-t ə-laska' has an insertion of /t/.",
  "Metathesis & Transposition":
    "Syllables or phonemes swap positions. Example: Montana ('mountain') where /ˈmɒn.tæ.nə/ becomes /ˈmaʊn.tən/.",
  "Semantic Elaboration":
    "A straightforward homophone is dressed up with a vivid mental image. Example: Texas ('taxes') where 'Tax-iz' is visualized as a towering pile of tax forms.",
  "Phonotactic Stretching":
    "The original word's phonemes are kept in order but stretched or 'stuttered' across extra syllables. Example: Mississippi ('Mrs. sip') where ˌmɪsɪˈsɪpi becomes Mrs. sip + trailing 'i'.",
  "Alliteration & Rhythmic Echo":
    "Emphasis on matching initial consonants or rhythmic feet. Example: Massachusetts ('mass chew sit') where /ˌmæsəˈtʃuːsɪts/ becomes 'mass-chew-sit'.",
};

const app = new Hono();

// Add a helper function to create language-specific prompts
const createLanguagePrompt = (language: string, basePrompt: string) => {
  return basePrompt.replace(/{language}/g, language);
};

app.post("/", async (c) => {
  const togetherai = new ChatTogetherAI({
    model: "deepseek-ai/DeepSeek-V3",
    temperature: 0,
  });

  const mistralai = new ChatMistralAI({
    model: "mistral-large-latest",
    temperature: 0,
  });

  const openai = new ChatOpenAI({
    model: "gpt-4o-mini",
  });

  const parser = new XMLParser({
    ignoreAttributes: false,
    isArray: (name) => name === "substitution",
  });

  let language = "english"; // Default value
  try {
    // Parse the JSON body with new parameters
    const {
      word,
      specificCategories,
      category,
      language: reqLanguage = "english",
      count = 5,
    } = await c.req.json<{
      word: string;
      specificCategories?: string[];
      category?: string;
      language?: string;
      count?: number;
    }>();
    language = reqLanguage; // Assign the requested language
    console.log(
      `Processing mnemonic request for word "${word}" in ${language}`
    );
    console.log(
      `Processing mnemonic request for word "${word}" in ${language}`
    );

    // If a specific category is requested, generate only for that category
    if (category) {
      const categoryPrompt = CATEGORY_SPECIFIC_PROMPT.replace(
        /{category}/g,
        category
      )
        .replace(
          /{category_description}/g,
          CATEGORY_DESCRIPTIONS[
            category as keyof typeof CATEGORY_DESCRIPTIONS
          ] || ""
        )
        .replace(/{language}/g, language)
        .replace(/{count}/g, count.toString());

      // Use a dedicated model instance for this language
      const languageModel = new ChatTogetherAI({
        model: "deepseek-ai/DeepSeek-V3",
        temperature: 0,
      });

      const litellmDeepSeek = new ChatOpenAI({
        model: "deepseek/deepseek-chat-v3-0324:free",
        configuration: {
          basePath: "https://litellm.tadzlab.xyz",
        },
        apiKey: process.env.LITELLM_API_KEY,
      });

      const AgentState = Annotation.Root({
        messages: Annotation<BaseMessage[]>({
          reducer: (x, y) => x.concat(y),
          default: () => [new SystemMessage(categoryPrompt)],
        }),
      });

      // Create a dedicated agent for this language
      const preAgent = new Agent({
        model: litellmDeepSeek,
        State: AgentState,
        tools: [searchtool],
      });
      const agent = preAgent.create();

      // Create a new human message with the input word
      const humanMessage = new HumanMessage(word);

      // Invoke the agent with the human message
      const response = await agent.invoke(
        { messages: [humanMessage] },
        { configurable: { thread_id: `mnemonic-category-${category}` } }
      );

      // Extract the last message from the response
      const lastMessage = response.messages[response.messages.length - 1];
      const xmlResponse = await new StringOutputParser().invoke(lastMessage);

      // Parse the XML to JSON
      const jsonObj = parser.parse(xmlResponse);

      return c.json(jsonObj);
    }

    // For initial request with or without specific categories
    const systemPrompt = MNEMONIC_SYSTEM_PROMPT.replace(
      /{language}/g,
      language
    );

    // Use a dedicated model instance for this language
    const languageModel = new ChatTogetherAI({
      model: "deepseek-ai/DeepSeek-V3",
      temperature: 0,
    });

    const litellmDeepSeek = new ChatOpenAI({
      model: "deepseek/deepseek-chat-v3-0324:free",
      configuration: {
        basePath: "https://litellm.tadzlab.xyz",
      },
      apiKey: process.env.LITELLM_API_KEY,
    });

    const AgentState = Annotation.Root({
      messages: Annotation<BaseMessage[]>({
        reducer: (x, y) => x.concat(y),
        default: () => [new SystemMessage(systemPrompt)],
      }),
    });

    const preAgent = new Agent({
      model: litellmDeepSeek,
      State: AgentState,
      tools: [searchtool],
    });
    const agent = preAgent.create();

    // Create a new human message with the input word
    const humanMessage = new HumanMessage(word);

    // Invoke the agent with the human message
    const response = await agent.invoke(
      { messages: [humanMessage] },
      { configurable: { thread_id: "mnemonic-analysis" } }
    );

    // Extract the last message from the response
    const lastMessage = response.messages[response.messages.length - 1];
    const xmlResponse = await new StringOutputParser().invoke(lastMessage);

    // Parse the XML to JSON
    const jsonObj = parser.parse(xmlResponse);

    // If specific categories are requested, generate those in parallel
    if (specificCategories && specificCategories.length > 0) {
      // Create a separate model for each category to enable true parallelism
      const categoryPromises = specificCategories.map(async (cat) => {
        const categoryModel = new ChatTogetherAI({
          model: "deepseek-ai/DeepSeek-V3",
          temperature: 0,
        });

        const litellmDeepSeek = new ChatOpenAI({
          model: "deepseek/deepseek-chat-v3-0324:free",
          configuration: {
            basePath: "https://litellm.tadzlab.xyz",
          },
          apiKey: process.env.LITELLM_API_KEY,
        });

        const categoryPrompt = CATEGORY_SPECIFIC_PROMPT.replace(
          /{category}/g,
          cat
        )
          .replace(
            /{category_description}/g,
            CATEGORY_DESCRIPTIONS[cat as keyof typeof CATEGORY_DESCRIPTIONS] ||
              ""
          )
          .replace(/{language}/g, language)
          .replace(/{count}/g, count.toString());

        const catAgentState = Annotation.Root({
          messages: Annotation<BaseMessage[]>({
            reducer: (x, y) => x.concat(y),
            default: () => [new SystemMessage(categoryPrompt)],
          }),
        });

        const catAgent = new Agent({
          model: litellmDeepSeek,
          State: catAgentState,
          tools: [searchtool],
        }).create();

        const catResponse = await catAgent.invoke(
          { messages: [new HumanMessage(word)] },
          { configurable: { thread_id: `mnemonic-category-${cat}` } }
        );

        const catLastMessage =
          catResponse.messages[catResponse.messages.length - 1];
        const catXmlResponse = await new StringOutputParser().invoke(
          catLastMessage
        );

        return parser.parse(catXmlResponse);
      });

      const categoryResults = await Promise.all(categoryPromises);

      // Merge all substitutions into the main result
      categoryResults.forEach((result) => {
        if (
          result.mnemonic_analysis &&
          result.mnemonic_analysis.substitutions &&
          result.mnemonic_analysis.substitutions.substitution
        ) {
          jsonObj.mnemonic_analysis.substitutions.substitution = [
            ...jsonObj.mnemonic_analysis.substitutions.substitution,
            ...result.mnemonic_analysis.substitutions.substitution,
          ];
        }
      });
    }

    return c.json(jsonObj);
  } catch (error: any) {
    console.error(`Error in mnemonic analysis route for ${language}:`, error);
    return c.json(
      { error: `Failed to generate mnemonic analysis for ${language}.` },
      500
    );
  }
});

export default app;
