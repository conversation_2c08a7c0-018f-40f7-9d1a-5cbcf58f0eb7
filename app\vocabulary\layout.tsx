"use client"
import { <PERSON>bar<PERSON>rov<PERSON>, SidebarTrigger, SidebarInset } from "@/components/ui/sidebar"
import { useManageChat } from "@/hooks/use-manage-chat";
import { Button } from "@/components/ui/button";
import { AppSidebar } from "@/components/app-siderbar";

type Props = {
  children: React.ReactNode;
};

export default function VocabularyLayout({ children }: Props) {
  const { isOpen, setIsOpen } = useManageChat();
  
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className="flex flex-col min-h-screen w-full bg-gray-50">
        <main className="flex-grow">
          <div className="flex items-center ml-10 m-2">
            <span className="text-sm border p-1 rounded-md" onClick={setIsOpen}>chat</span>
            <SidebarTrigger />
          </div>
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
  }