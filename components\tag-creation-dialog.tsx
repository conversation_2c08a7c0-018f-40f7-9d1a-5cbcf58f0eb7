'use client'

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { Plus } from 'lucide-react';

type Tag = {
  id: string;
  name: string;
  description?: string;
};

interface TagCreationDialogProps {
  onTagCreated: (tag: Tag) => void;
  trigger?: React.ReactNode;
}

export function TagCreationDialog({ onTagCreated, trigger }: TagCreationDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tagName, setTagName] = useState('');
  const [tagDescription, setTagDescription] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleCreateTag = async () => {
    if (!tagName.trim()) {
      toast({
        title: "Validation Error",
        description: "Tag name is required.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/custom-settings/tags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          name: tagName.trim(), 
          description: tagDescription.trim() || undefined 
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create tag');
      }

      const createdTag: Tag = await response.json();
      onTagCreated(createdTag);
      
      // Reset form
      setTagName('');
      setTagDescription('');
      setIsOpen(false);
      
      toast({
        title: "Success",
        description: "Tag created successfully.",
      });
    } catch (error) {
      console.error("Error creating tag:", error);
      toast({
        title: "Error",
        description: `Failed to create tag: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="w-full">
      <Plus className="h-4 w-4 mr-2" />
      Create New Tag
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Tag</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="tag-name">Tag Name</Label>
            <Input
              id="tag-name"
              value={tagName}
              onChange={(e) => setTagName(e.target.value)}
              placeholder="e.g., Grammar, Vocabulary"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleCreateTag();
                }
              }}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="tag-description">Description (Optional)</Label>
            <Textarea
              id="tag-description"
              value={tagDescription}
              onChange={(e) => setTagDescription(e.target.value)}
              placeholder="Brief description of what this tag represents..."
              rows={3}
            />
          </div>
        </div>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleCreateTag} disabled={isLoading}>
            {isLoading ? 'Creating...' : 'Create Tag'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
