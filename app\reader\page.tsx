"use client"
import { Input } from "@/components/ui/input"
import MarkdownTypewriter from '@/components/markdowntyper'
import { But<PERSON> } from "@/components/ui/button"
import { useState } from "react"
import { TextSelectionActions } from "@/components/text-selection";
import { basicActions, actions } from "@/lib/actions-text-selections";
import { useManageChat } from "@/hooks/use-manage-chat"






export default function Page (){

    
    const [text,setText] = useState("Hello Word")
    const [input,setInput] = useState("")
    const instruction =""
    const [isLoading,setIsLoading] = useState(false)
    const {isOpen,setIsOpen} = useManageChat()


    const handleSubmit = async () => {
        if (!text?.trim()) {
            return;
        }
  
        setIsLoading(true);
        try {
            const response = await fetch('/api/chat/multifunc', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    input,
                    instruction: 'provide me a formated Markdown text from the input with the corresponding IPA spelling of each word infront of him.so provide me the word and is IPA spelling next to him in a way that i can read nornaly ad take a look on the spelling. allways put the ipa spelling in bold',
                }),
            });
  
            if (!response.ok) {
                throw new Error('LLM request failed.');
            }
  
            const data = await response.json();
            setText(data.text);
  
  
        } catch (error) {
  
            console.error("Error with Custom Query:", error);
            setText("Failed to fetch response.");
  
        } finally {
  
            setIsLoading(false);
            setInput("")
  
        }
    };


    return(
        <main className=" m-12 lg:m-20" >
            <div className="flex gap-2">
                <Input value={input}
                        onChange={(e) => setInput(e.target.value)} />
                <Button disabled={isLoading} onClick={handleSubmit} >Submit</Button>
                <Button onClick={setIsOpen} >Chat</Button>
            </div>
            <TextSelectionActions
                    chat={true}
                    translate={true}
                    smallDiplay={true} 
                    actions={
                    [
                        ...basicActions,            
                        actions.handleSynonyms,
                        actions.handleExamples,
                        actions.handleIPA,
                        actions.handleAntonyms
                    ]
                    }
                    >
            <div className="mt-4 p-8 flex items-center bg-slate-50 rounded-md">
                <MarkdownTypewriter content={text} initialDelay={0} typeSpeed={1} />
            </div>
            </TextSelectionActions>
        </main>
    )
}