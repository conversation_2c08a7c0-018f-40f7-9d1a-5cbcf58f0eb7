// app/page.tsx
"use client";

import React, { useState, useCallback } from "react";
/* import TextSelectionAction from "@/components/text-selection"; */
/* import ReactQuill from "react-quill"; */
import 'react-quill/dist/quill.snow.css'; 
import { compiledConvert } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Volume } from "lucide-react";
import dynamic from 'next/dynamic'
import { ChatForm } from "@/components/chat";

const TextSelectionAction = dynamic(() => import('@/components/text-selection'), {
  ssr: false,
  loading: () => <p>Loading editor...</p>,
})
const ReactQuill = dynamic(() => import("react-quill"), {
  ssr: false,
  loading: () => <p>Loading editor...</p>,
})

export default function Home() {
  const [text, setText] = useState("");
  const [ipa, setIpa] = useState("");
  const [loading, setLoading] = useState(false);
  const [readText,SetReadText] = useState("")



  const handleTextToSpeech = useCallback(() => {
    
        const utterance = new SpeechSynthesisUtterance(readText)
        window.speechSynthesis.speak(utterance)    
      }, [readText])
  

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    setIpa("");
    
    
    const formated = compiledConvert(text)
    SetReadText(formated)

    try {
      const res = await fetch("/api/ipa", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      body: JSON.stringify({ text: formated }),
      });

      if (!res.ok) {
        throw new Error("Failed to fetch IPA transcription");
      }

      const data = await res.json();
      setIpa(data.ipa);
    } catch (error) {
      console.error("Error:", error);
      setIpa("An error occurred while fetching the IPA transcription.");
    } finally {
      setLoading(false);
    }
  };

  return (

    <main className="flex" >
      <div className="flex flex-1 min-h-screen flex-col items-center justify-center bg-gray-100 p-4">
      
        <div className="max-w-lg w-full bg-white p-6 rounded shadow">
          <h1 className="text-2xl font-bold mb-4">IPA Transcription App</h1>
          <form onSubmit={handleSubmit} className="flex flex-col space-y-4">
            <label htmlFor="textInput" className="font-medium">
              Enter text to transcribe:
            </label>
            <TextSelectionAction>
            <ReactQuill theme="snow" value={text} onChange={setText} />
              {/* <textarea
                id="textInput"
                className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={5}
                value={text}
                onChange={(e) => setText(e.target.value)}
                placeholder="Type your text here..."
              /> */}
              
            </TextSelectionAction>
            <button
              type="submit"
              className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
              disabled={loading}
            >
              {loading ? "Transcribing..." : "Get IPA"}
            </button>
          </form>
          
            {ipa && (
              
                <TextSelectionAction className={"mt-4 p-4 border border-gray-300 rounded bg-gray-50"} >
                  <h2 className="font-semibold mb-2">IPA Transcription:</h2>
                  <Button onClick={handleTextToSpeech} >
                    Speak
                  </Button>
                  <p className="whitespace-pre-wrap">{ipa}</p>
                </TextSelectionAction>
              
            )}
          
        </div>
      
    </div>
      {/* <div className="w-[35%]" >
        <ChatForm/>
      </div> */}
    </main>

  );
}
