import { Hono } from "hono";
import {
  HumanMessage,
  SystemMessage,
  BaseMessage,
} from "@langchain/core/messages";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { Annotation } from "@langchain/langgraph";
import { sp_IPA } from "@/lib/prompt";
import { tool } from "@langchain/core/tools";
import { ChatOpenAI } from "@langchain/openai";
import { ChatTogetherAI } from "@langchain/community/chat_models/togetherai";
import { Agent } from "@/lib/ai-functions";
import { z } from "zod";
import { schemaThemes } from "@/lib/schemas";
import { ChatMistralAI } from "@langchain/mistralai";

const searchtool = tool(
  (input) => {
    return "search result";
  },
  {
    name: "searchtool",
    description: "search tool",
    schema: z.string().describe("search to be made"),
  }
);

const app = new Hono().post("/", async (c) => {
  const openai = new ChatOpenAI({
    model: "gpt-4o-mini",
  }); /* .withStructuredOutput(schemaThemes) */

  const togetherai = new ChatTogetherAI({
    model: "deepseek-ai/DeepSeek-V3",
    temperature: 0,
  }); /* .withStructuredOutput(schemaThemes) */

  const mistralai = new ChatMistralAI({
    model: "mistral-large-latest",
    temperature: 0,
  }); /* .withStructuredOutput(schemaThemes) */

  const AgentState = Annotation.Root({
    messages: Annotation<BaseMessage[]>({
      reducer: (x, y) => x.concat(y),
      default: () => [new SystemMessage(sp_IPA)],
    }),
  });

  const preAgent = new Agent({
    model: openai,
    State: AgentState,
    tools: [searchtool],
  });
  const agent = preAgent.create();

  let success = false;
  let attempts = 0;

  while (!success && attempts < 5) {
    attempts += 1;
    try {
      // Parse the JSON body
      const { text } = await c.req.json();
      console.log("Received text:", text);

      // Create a new human message with the input text
      const humanMessage = new HumanMessage(text);

      // Invoke the agent with the human message
      const response = await agent.invoke(
        { messages: [humanMessage] },
        { configurable: { thread_id: "1" } }
      );

      // Extract the last message from the response
      const lastMessage = response.messages[response.messages.length - 1];
      console.log("Last Message:", lastMessage);

      // Parse the response using StringOutputParser
      const parsedResponse = await new StringOutputParser().invoke(lastMessage);
      console.log("Parsed IPA Transcription:", parsedResponse);

      // Return the IPA transcription as JSON
      return c.json({ ipa: parsedResponse });
    } catch (error: any) {
      console.error("Error fetching IPA transcription:", error);
      // Return a 500 error with a JSON error message
    }
  }

  return c.json({ error: "Failed to fetch IPA transcription." }, 500);
});

export default app;
