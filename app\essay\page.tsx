import TranslatorPage from "@/components/traductor"
import dynamic from "next/dynamic";
import WordDiscover from "@/components/word-discover";
const Essay = dynamic(() => import('@/components/essay'), {
    ssr: false,
    loading: () => <p>Loading ...</p>,
  })

export default function Page(){

    return (
    
    <main>

        <div className="" >
            <Essay  title="Hello World" />
        </div>
        <div className=" grid grid-cols-2  h-fit">
            <TranslatorPage/>
            <WordDiscover/>
        </div>
    </main>
)
} 