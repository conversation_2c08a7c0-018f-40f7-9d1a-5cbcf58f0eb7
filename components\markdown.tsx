"use client"

import React, { FC, useMemo,useEffect } from 'react';
import { marked } from 'marked';
import DOMPurify from 'dompurify';

interface MarkdownFormatterProps {
  /** The Markdown input string */
  markdown: string;
  /** Optional CSS class for the container */
  className?: string;
}

/**
 * MarkdownFormatter
 *
 * Converts Markdown to sanitized HTML and renders it.
 */
const MarkdownFormatter  = ({ markdown, className }:MarkdownFormatterProps) => {

  /**
   * Sanitize and convert Markdown to HTML.
   * The useMemo ensures that the computation only happens when `markdown` changes.
   * 
   * 
   */    
  
    const sanitizedHtml = useMemo(() => {
    const rawHtml = marked.parse(markdown,{async:false}); // Convert Markdown to HTML
    return DOMPurify.sanitize(rawHtml);    // Sanitize the HTML
  }, [markdown]);


  return (
    <div
      className={className}
      dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
    />
  );
};

export default MarkdownFormatter;
