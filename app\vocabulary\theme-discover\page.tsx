"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button"; // Shadcn
import {set, z} from "zod"
import { useManageEssay } from "@/hooks/use-manage-essay";
import dynamic from 'next/dynamic'
import { newThemeDiscover } from "@/lib/schemas";
import { Badge } from "@/components/ui/badge"
import CardDisplay from "@/components/card";
import DisplayWord from "@/components/displaywords";
import { useManageDisplayTheme } from "@/hooks/use-manage-theme-display";
import axios from "axios";
import TranslatorPage from "@/components/traductor";


type ResponseRequest = z.infer<typeof newThemeDiscover>
 

const Essay = dynamic(() => import('@/components/essay'), {
    ssr: false,
    loading: () => <p>Loading ...</p>,
  })

//TODO add the IPA spelling.
//TODO change the display.
//TODO make the components collapseble.
//TODO add speak on Cha<PERSON>  and oder pages.
//TODO add modes chat.
//TODO verb-noun-adjective-adverb connections.
//TODO revise vocabulary.
//TODO small functions.
//TODO sugestion to continue the conversation.

export default function ThemeVocabularyPage() {
  const { isOpen, setIsOpen } = useManageEssay();
 
  
  const [input, setInput] = useState("")
  const {vocabList, setVocabList, theme : themeDisplay, deleteTheme } = useManageDisplayTheme();
  const [theme, setTheme] = useState("");
  const [displayedTheme,setIsDisplayedTheme] = useState<ResponseRequest | undefined >(undefined)
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [isDisplayed,setIsDisplayed] = useState("")
  const [subCategories, setSubCategories] = useState<{
    theme:string,
    subCategories: string[],
  }[]>([])

  console.log(subCategories)

  const [nounActive,setNounActive] = useState(true)
  const [verbActive,setVerbActive] = useState(false)
  const [adverbActive,setAdverbActive] = useState(false)
  const [adjectiveActive,setAdjectiveActive] = useState(false)

  const handleCreateTheme = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setVocabList(undefined);
    setError("");
    console.log(input)

    try {
      console.log(input)

      const res = await fetch("/api/vocabulary/theme-vocab", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ input }),
      });
      if (!res.ok) {
        throw new Error("Failed to fetch theme-based vocabulary.");
      }

      const response:ResponseRequest = await res.json();
      setSubCategories((prev)=>([  ...(prev || []),{theme:response.theme, subCategories: Array.isArray(response.subCategory) ? response.subCategory : [response.subCategory]}]))
      setIsDisplayedTheme(response);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchTheme = async () => {
    if (!themeDisplay.id) {
      setError('Please enter a Theme ID.');
      setTheme("");
      return;
    }

    setLoading(true);
    setError("");
    setTheme("");

    try {
      const response = await axios.get(`/api/vocabulary/theme/${encodeURIComponent(themeDisplay.id)}`);

      // Axios automatically parses JSON responses
      const {data} = response.data;
      
      console.log(data)

      if (data.success === false) {
        // Handle custom error messages from the API
        throw new Error(data.error || 'An unknown error occurred.');
      }

      setIsDisplayed(data.theme)
      setIsDisplayedTheme(data)
      setVocabList(data)
      setSubCategories((prev)=>([  ...(prev || []),{theme:data.theme, subCategories:data.subCategory}]))
    } catch (err:any) {
      console.error('Error fetching theme:', err);
      setError(err.message || 'An error occurred while fetching the theme.');
    } finally {
      setLoading(false);
    }
  };

  const fetchSub = async (name:String) => {

    setLoading(true);
    setError("");
    setTheme("");

    try {
      const response = await axios.get(`/api/vocabulary/theme-name`,{
        params: {
          name: name,
        },
      });

      // Axios automatically parses JSON responses
      const {data} = response.data;
     

      if (data.success === false) {
        // Handle custom error messages from the API
        throw new Error('Item not found.');
      }
      setSubCategories((prev)=>([  ...(prev || []),{theme:data.theme, subCategories:data.subCategory}]))
      console.log(data)
      setIsDisplayed(data.theme)
      setIsDisplayedTheme(data)

    } catch (err:any) {
      console.error('Error fetching theme:', err);
      setError('Item not found.');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectedSub = async (e:React.MouseEvent<HTMLButtonElement>) => {
    const sub = e.currentTarget.textContent
    if (!sub) {
      return;
    }

    fetchSub(sub)
    console.log(sub)

  }

  const getSubCategoriesByTheme = (theme: string) => {
    const result = subCategories.find((item) => item.theme === theme);
    return result ? result.subCategories : null; // Return subCategories or null if not found
  };


 useEffect(
    ()=>{
      fetchTheme()
      console.log(themeDisplay)
    }
  ,[themeDisplay])


  useEffect(() => {
    setIsDisplayedTheme(vocabList);
  }, [])


 //TODO pass is opem


  return (
    <main className="min-h-screen w-full overflow-y-auto  p-6">
      <TranslatorPage/>
      <div className="w-full h-full bg-white p-6 rounded shadow space-y-6">
        <h1 className="text-2xl font-bold">Theme-based Vocabulary</h1>

        {/* THEME INPUT */}
        <form onSubmit={handleCreateTheme} className="flex flex-col lg:flex-row gap-4">
          <input
            type="text"
            className="border border-gray-300 p-2 rounded flex-1"
            placeholder="Type a theme (e.g. 'Travel')"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            required
          />
          <Button type="submit" disabled={loading}>
            {loading ? "Loading..." : "Get Vocabulary"}
          </Button>
        </form>
        {
            vocabList && <Button onClick={()=>{setIsOpen(prev => !prev)}} >
                write something about {theme}
            </Button>
        }

        {error && (
          <div className="text-red-600 border border-red-200 p-2 rounded">
            {error}
          </div>
        )}

        {/* VOCAB LIST */}
        {vocabList && 
        <div>
          <div >

            <Button className="text-md" variant={ isDisplayed === vocabList.theme ? "destructive" : "outline"}>{vocabList.theme}</Button>
            <div>
              <div className="my-2" >sub Categories : {vocabList.theme} </div>
              <div className="my-2 flex flex-wrap gap-2" >      
               {
                getSubCategoriesByTheme(vocabList.theme) &&
                ( getSubCategoriesByTheme(vocabList.theme)?.map((s)=>{
                    return (
                      <>
                        <Button onClick={handleSelectedSub} className="text-md" variant={ isDisplayed === s ? "destructive" : "outline"}>{s}</Button>
                        
                      </>
                    )
                }))
              }
              

            </div>
                    {
                         isDisplayed != vocabList.theme && 
                         <div>
                          <div className="my-2" > sub Categories : {isDisplayed} </div>
                          <div className="my-2 flex gap-2 flex-wrap" >
                            {
                            getSubCategoriesByTheme(isDisplayed)?.map((
                                subCat
                            )=>{
                              return(
                                <div>        
                                  <Button onClick={handleSelectedSub} className="text-md" variant={ isDisplayed === subCat ? "destructive" : "outline"}>{subCat}</Button>
                                </div>
                              )
                            })
                          }
                          </div>
                         </div> 
                    }
            </div>

          </div> 
          <div className="flex flex-col gap-5">
            <div className="my-4 flex flex-wrap gap-2">
             <Button onClick={()=>{setNounActive(prev => !prev)}} variant={nounActive ? "default" : "outline"} >nouns</Button>
             <Button onClick={()=>{setVerbActive(prev => !prev)}} variant={verbActive ? "default" :"outline"} >verbs</Button>
             <Button onClick={()=>{setAdverbActive(prev => !prev)}} variant={adverbActive ? "default" :"outline"} >adverbs</Button>
             <Button onClick={()=>{setAdjectiveActive(prev => !prev)}} variant={adjectiveActive ? "default" :"outline"} >adjectives</Button> 
            </div>
            
              {nounActive && <div>
                <div className="text-xl" >Nouns</div>
                <DisplayWord type={"nouns"}  vocabList={displayedTheme} />
                </div>}
              {verbActive && <div>
                <div className="text-xl" >Verbs</div>
                <DisplayWord type={"verbs"} vocabList={displayedTheme} />
              </div> }
              {adverbActive && <div>
                <div className="text-xl" >Adverbs</div>
                <DisplayWord type={"adverbs"} vocabList={displayedTheme} />
              </div> }
              {adjectiveActive && <div>
                <div className="text-xl" >Adjectives</div>
                <DisplayWord type={"adjectives"} vocabList={displayedTheme} />
              </div> }
          </div>
        </div>}
        {vocabList && (
          <Button 
            variant="destructive" 
            onClick={async () => {
              if (window.confirm('Are you sure you want to delete this theme?')) {
                const success = await deleteTheme(themeDisplay.id);
                if (success) {
                  setVocabList(undefined);
                  setIsDisplayed("");
                  setIsDisplayedTheme(undefined);
                } else {
                  setError("Failed to delete theme");
                }
              }
            }}
          >
            Delete Theme
          </Button>
        )}
      </div>
    {isOpen && <div className="w-full h-full bg-white p-6 rounded shadow space-y-6" ><Essay topic={"travel"} title={`write something about ${theme}`} /></div>}
    </main>
  );
}

//TODO modify schema to find display words, Verbs, expressions

//TODO copy fuctionnality
// A simple placeholder for your learning tools

function LearningTools({ vocabList }: { vocabList: ResponseRequest[] }) {
  const [mode, setMode] = useState<"none" | "flashcards" | "quiz">("none");

  const handleModeChange = (newMode: "flashcards" | "quiz") => {
    setMode(newMode);
  };

  if (!vocabList || vocabList.length === 0) return null;

  return (
    <div className="space-y-2">
      <h3 className="font-bold">Practice</h3>
      <div className="flex gap-2">
        <Button variant="secondary" onClick={() => handleModeChange("flashcards")}>
          Flashcard Mode
        </Button>
        <Button variant="secondary" onClick={() => handleModeChange("quiz")}>
          Quiz Mode
        </Button>
      </div>

      {/* Simple placeholders: Expand as needed */}
      {mode === "flashcards" && <FlashcardView vocabList={vocabList} />}
      {mode === "quiz" && <QuizView vocabList={vocabList} />}
    </div>
  );
}

function FlashcardView({ vocabList }: { vocabList: ResponseRequest[] }) {
  // Basic flashcard approach: show one word at a time, let user flip for meaning.
  // Implementation is up to you. For brevity, we’ll just show a placeholder.
  return (
    <div className="border p-4 rounded">
      <p className="text-gray-600 italic">
        [Flashcard Mode Placeholder — you could show each word's front/back here!]
      </p>
    </div>
  );
}

function QuizView({ vocabList }: { vocabList: ResponseRequest[] }) {
  // Basic quiz approach: multiple choice or fill-in-the-blank.
  // Implementation is up to you. For brevity, we’ll just show a placeholder.
  return (
    <div className="border p-4 rounded">
      <p className="text-gray-600 italic">
        [Quiz Mode Placeholder — you could ask the user to match synonyms or provide a translation!]
      </p>
    </div>
  );
}
