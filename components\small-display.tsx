"use client";

import React,{useState, useEffect} from "react";
import { useManageSmallDisplay } from "@/hooks/use-manage-small-display";// Update the path accordingly
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverArrow,
  PopoverClose,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import dynamic from "next/dynamic";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { LoaderIcon, Feather } from "lucide-react"; // Replace with your actual icon
import { useSidebar } from "./ui/sidebar";

const MarkdownFormatter = dynamic(() => import("./markdown"), {
  ssr: false,
  loading: () => <p>Loading formatter...</p>, // Optional: Loading fallback
});

const SmallDisplay: React.FC = () => {
  const { outputSD, inputSD, isLoading } = useManageSmallDisplay();
  const [output,setOutput] = useState("")


  useEffect(()=>{
    setOutput(outputSD)
  },[outputSD])

  return (
    <Card className="w-full mt-2   shadow-lg">
      <CardHeader className=" p-0 pb-1 pl-2">
        <CardTitle className="flex items-center text-sm text-slate-600" >
            small Screen
            <Popover>
                <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" aria-label="View Input">
                    < Feather />
                    </Button>
                </PopoverTrigger>
                <PopoverContent>
                    <div className="font-semibold mb-2">Your Input:</div>
                    <div>{inputSD || "No input provided."}</div>
                </PopoverContent>
                </Popover>
        </CardTitle>
      </CardHeader>
      <CardContent className=" max-h-56 overflow-y-auto p-2 rounded-md m-1 ">
        {isLoading ? (
         <div className="flex items-center justify-center" >
            <LoaderIcon className="animate-spin "  size={18} />
         </div> 
        ) : (
            <MarkdownFormatter className=" text-[15px]" markdown={output || ""} />
        )}
      </CardContent>
    </Card>
  );
};

export default SmallDisplay;
