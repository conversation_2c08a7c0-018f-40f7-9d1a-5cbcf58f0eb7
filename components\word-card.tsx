"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { z } from "zod";
import {
    <PERSON>ccordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
  } from "@/components/ui/accordion"
  

export const wordSchema = z.object({
  primaryMeaning: z.array(
    z.object({
      meaning: z.string(),
      translation: z.string(),
    })
  ),
  usageFrequency: z.object({
    speaking: z.object({
      frequency: z.number().min(1).max(10),
      examples: z.array(z.string()),
      mostUsedWord: z
        .object({
          word: z.string(),
          example: z.string(),
        })
        .optional(),
    }),
    writing: z.object({
      frequency: z.number().min(1).max(10),
      examples: z.array(z.string()),
      mostUsedWord: z
        .object({
          word: z.string(),
          example: z.string(),
        })
        .optional(),
    }),
  }),
  exampleSentences: z
    .array(
      z.object({
        sentence: z.string(),
        translation: z.string(),
      })
    )
    .min(1)
    .max(3),
  otherMeanings: z.array(
    z.object({
      meaning: z.string(),
      example: z.string(),
    })
  ),
  synonyms: z.array(z.string()),
});

type WordData = z.infer<typeof wordSchema>;

interface WordCardProps {
  data: WordData;
}

const WordCard: React.FC<WordCardProps> = ({ data }) => {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Word Information</CardTitle>
        <CardDescription>Details about the word's usage and meanings</CardDescription>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-2">
                       {/* Synonyms */}
        <section className="mb-6">
          <h3 className="font-semibold text-lg mb-4">Synonyms</h3>
          <ul>
            {data.synonyms.map((synonym, index) => (
              <li key={index}>- {synonym}</li>
            ))}
          </ul>
        </section>
        {/* Primary Meanings */}
        <section className="mb-6">
          <h3 className="font-semibold text-lg mb-2">Primary Meanings</h3>
          {data.primaryMeaning.map((item, index) => (
            <div key={index} className="mb-4">
              <p>
                <strong>Meaning:</strong> {item.meaning}
              </p>
              <p>
                <strong>Translation:</strong> {item.translation}
              </p>
            </div>
          ))}
        </section>
        </div>


        {/* Usage Frequency */}
        <section className="mb-6">
          <h3 className="font-semibold text-lg mb-4">Usage Frequency</h3>
          <div className="grid grid-cols-2 gap-4">
            {/* Speaking */}
            <div>
              <h4 className="text-md font-semibold mb-2">Speaking</h4>
              <Progress value={(data.usageFrequency.speaking.frequency / 10) * 100} className="mb-2" />
              <p>
                <strong>Frequency:</strong> {data.usageFrequency.speaking.frequency}/10
              </p>
              <ul className="mt-2">
                {data.usageFrequency.speaking.examples.map((example, idx) => (
                  <li key={idx} className="mb-1">
                    - {example}
                  </li>
                ))}
              </ul>
              {data.usageFrequency.speaking.mostUsedWord && (
                <div className="mt-2">
                  <p>
                    <strong>Most Used Word:</strong> {data.usageFrequency.speaking.mostUsedWord.word}
                  </p>
                  <p>
                    <strong>Example:</strong> {data.usageFrequency.speaking.mostUsedWord.example}
                  </p>
                </div>
              )}
            </div>

            {/* Writing */}
            <div>
              <h4 className="text-md font-semibold mb-2">Writing</h4>
              <Progress value={(data.usageFrequency.writing.frequency / 10) * 100} className="mb-2" />
              <p>
                <strong>Frequency:</strong> {data.usageFrequency.writing.frequency}/10
              </p>
              <ul className="mt-2">
                {data.usageFrequency.writing.examples.map((example, idx) => (
                  <li key={idx} className="mb-1">
                    - {example}
                  </li>
                ))}
              </ul>
              {data.usageFrequency.writing.mostUsedWord && (
                <div className="mt-2">
                  <p>
                    <strong>Most Used Word:</strong> {data.usageFrequency.writing.mostUsedWord.word}
                  </p>
                  <p>
                    <strong>Example:</strong> {data.usageFrequency.writing.mostUsedWord.example}
                  </p>
                </div>
              )}
            </div>
          </div>
        </section>
        <Accordion type="single" collapsible>
        <AccordionItem value="item-1">
            <AccordionTrigger>more</AccordionTrigger>
            <AccordionContent>        
                {/* Example Sentences */}
                <section className="mb-6">
                <h3 className="font-semibold text-lg mb-4">Example Sentences</h3>
                {data.exampleSentences.map((example, index) => (
                    <div key={index} className="mb-4">
                    <p>
                        <strong>Sentence:</strong> {example.sentence}
                    </p>
                    <p>
                        <strong>Translation:</strong> {example.translation}
                    </p>
                    </div>
                ))}
                </section>

                {/* Other Meanings */}
                <section className="mb-6">
                <h3 className="font-semibold text-lg mb-4">Other Meanings</h3>
                {data.otherMeanings.map((meaning, index) => (
                    <div key={index} className="mb-4">
                    <p>
                        <strong>Meaning:</strong> {meaning.meaning}
                    </p>
                    <p>
                        <strong>Example:</strong> {meaning.example}
                    </p>
                    </div>
                ))}
                </section>
            </AccordionContent>
        </AccordionItem>
        </Accordion>



      </CardContent>

      <CardFooter>
        <Button>Learn More</Button>
      </CardFooter>
    </Card>
  );
};

export default WordCard;
