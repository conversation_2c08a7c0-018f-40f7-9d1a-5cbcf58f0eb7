"use client"

import WordDiscover from "@/components/word-discover"
import TranslatorPage from "@/components/traductor"
import { use, useEffect } from "react"
import { useManageChat } from "@/hooks/use-manage-chat"
import SmallDisplay from "@/components/small-display"

export default function Page(){

    const { isOpen, setIsOpen ,toggleOpen } = useManageChat();

    useEffect(() => {

        toggleOpen(false)

    }, [])


  return(
    <div className=" flex flex-col gap-2 m-1" >
      <div>
        <SmallDisplay/>
      </div>
      
      <TranslatorPage />
    </div>
  )
}